{"ast": null, "code": "import { getPath } from '@mui/system/style';\nimport { alpha } from '@mui/system/colorManipulator';\nconst getTextDecoration = _ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  const transformedColor = ownerState.color;\n  // check the `main` color first for a custom palette, then fallback to the color itself\n  const color = getPath(theme, `palette.${transformedColor}.main`, false) || getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}.mainChannel`) || getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "alpha", "getTextDecoration", "_ref", "theme", "ownerState", "transformedColor", "color", "channelColor"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Link/getTextDecoration.js"], "sourcesContent": ["import { getPath } from '@mui/system/style';\nimport { alpha } from '@mui/system/colorManipulator';\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = ownerState.color;\n  // check the `main` color first for a custom palette, then fallback to the color itself\n  const color = getPath(theme, `palette.${transformedColor}.main`, false) || getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}.mainChannel`) || getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,KAAK,QAAQ,8BAA8B;AACpD,MAAMC,iBAAiB,GAAGC,IAAA,IAGpB;EAAA,IAHqB;IACzBC,KAAK;IACLC;EACF,CAAC,GAAAF,IAAA;EACC,MAAMG,gBAAgB,GAAGD,UAAU,CAACE,KAAK;EACzC;EACA,MAAMA,KAAK,GAAGP,OAAO,CAACI,KAAK,EAAE,WAAWE,gBAAgB,OAAO,EAAE,KAAK,CAAC,IAAIN,OAAO,CAACI,KAAK,EAAE,WAAWE,gBAAgB,EAAE,EAAE,KAAK,CAAC,IAAID,UAAU,CAACE,KAAK;EACnJ,MAAMC,YAAY,GAAGR,OAAO,CAACI,KAAK,EAAE,WAAWE,gBAAgB,cAAc,CAAC,IAAIN,OAAO,CAACI,KAAK,EAAE,WAAWE,gBAAgB,SAAS,CAAC;EACtI,IAAI,MAAM,IAAIF,KAAK,IAAII,YAAY,EAAE;IACnC,OAAO,QAAQA,YAAY,SAAS;EACtC;EACA,OAAOP,KAAK,CAACM,KAAK,EAAE,GAAG,CAAC;AAC1B,CAAC;AACD,eAAeL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}