{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DattaAbleLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport { useLocation } from 'react-router-dom';\nimport DattaAbleHeader from './DattaAbleHeader';\nimport DattaAbleSidebar from './DattaAbleSidebar';\nimport DattaAbleFooter from './DattaAbleFooter';\nimport DattaAbleBreadcrumbs from './DattaAbleBreadcrumbs';\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport '@fontsource/open-sans/300.css';\nimport '@fontsource/open-sans/400.css';\nimport '@fontsource/open-sans/500.css';\nimport '@fontsource/open-sans/600.css';\nimport '@fontsource/open-sans/700.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DattaAbleLayout = ({\n  children\n}) => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const location = useLocation();\n\n  // Close sidebar on route change (mobile)\n  useEffect(() => {\n    setSidebarOpen(false);\n  }, [location.pathname]);\n\n  // Apply CSS variables to document root\n  useEffect(() => {\n    const root = document.documentElement;\n    Object.entries(dattaAbleTheme.cssVariables).forEach(([key, value]) => {\n      root.style.setProperty(key, value);\n    });\n  }, []);\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n  const toggleSidebarCollapse = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n  const layoutStyles = {\n    minHeight: '100vh',\n    backgroundColor: dattaAbleTheme.colors.background.default,\n    fontFamily: dattaAbleTheme.typography.fontFamily\n  };\n  const mainContentStyles = {\n    marginLeft: sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,\n    transition: 'margin-left 0.3s ease',\n    minHeight: '100vh',\n    display: 'flex',\n    flexDirection: 'column'\n  };\n  const contentWrapperStyles = {\n    flex: 1,\n    padding: dattaAbleTheme.spacing[4],\n    paddingTop: `calc(${dattaAbleTheme.layout.header.height} + ${dattaAbleTheme.spacing[4]})`\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: layoutStyles,\n    children: [/*#__PURE__*/_jsxDEV(DattaAbleSidebar, {\n      isOpen: sidebarOpen,\n      isCollapsed: sidebarCollapsed,\n      onToggle: toggleSidebar,\n      onCollapse: toggleSidebarCollapse\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...mainContentStyles,\n        marginLeft: window.innerWidth < 768 ? 0 : sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width\n      },\n      children: [/*#__PURE__*/_jsxDEV(DattaAbleHeader, {\n        onToggleSidebar: toggleSidebar,\n        onToggleSidebarCollapse: toggleSidebarCollapse,\n        sidebarCollapsed: sidebarCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: contentWrapperStyles,\n        children: [/*#__PURE__*/_jsxDEV(DattaAbleBreadcrumbs, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Container, {\n          fluid: true,\n          className: \"px-0\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              children: children\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DattaAbleFooter, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), sidebarOpen && window.innerWidth < 768 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        zIndex: 1040\n      },\n      onClick: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        /* Global body styling for dark theme */\n        body {\n          background-color: ${dattaAbleTheme.colors.background.default} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Override any remaining white backgrounds */\n        * {\n          scrollbar-width: thin;\n          scrollbar-color: ${dattaAbleTheme.colors.text.secondary} ${dattaAbleTheme.colors.background.light};\n        }\n\n        /* Custom scrollbar */\n        ::-webkit-scrollbar {\n          width: 6px;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: ${dattaAbleTheme.colors.background.light};\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: ${dattaAbleTheme.colors.text.secondary};\n          border-radius: ${dattaAbleTheme.borderRadius.full};\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        /* Responsive adjustments */\n        @media (max-width: 767.98px) {\n          .main-content {\n            margin-left: 0 !important;\n          }\n        }\n\n        /* Animation classes */\n        .fade-in {\n          animation: fadeIn 0.3s ease-in;\n        }\n\n        @keyframes fadeIn {\n          from {\n            opacity: 0;\n            transform: translateY(10px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        /* Bootstrap overrides for Datta Able styling */\n        .card {\n          border-radius: ${dattaAbleTheme.borderRadius.lg};\n          box-shadow: ${dattaAbleTheme.shadows.sm};\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .card-header {\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .card-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .text-muted {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Material-UI component overrides */\n        .MuiPaper-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiCard-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiDialog-paper {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiTypography-root {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiTypography-body2 {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Stepper components */\n        .MuiStepper-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label.Mui-active {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label.Mui-completed {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Input and form components */\n        .MuiOutlinedInput-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiOutlinedInput-notchedOutline {\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .MuiInputLabel-root {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Alert components */\n        .MuiAlert-root {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Bootstrap Alert overrides */\n        .alert-info {\n          --bs-alert-bg: #6c757d !important; /* Gray background */\n          --bs-alert-border-color: #5a6268 !important;\n          --bs-alert-color: #ffffff !important;\n        }\n\n        .btn {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.medium};\n        }\n\n        .form-control {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .form-control:focus {\n          border-color: ${dattaAbleTheme.colors.primary.main};\n          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25;\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .form-control::placeholder {\n          color: ${dattaAbleTheme.colors.text.secondary};\n        }\n\n        /* Custom utility classes */\n        .text-primary-custom {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .bg-primary-custom {\n          background-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .border-primary-custom {\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .shadow-custom {\n          box-shadow: ${dattaAbleTheme.shadows.md} !important;\n        }\n\n        /* Wallet specific styling */\n        .wallet-card {\n          background: linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%);\n          color: white;\n          border-radius: ${dattaAbleTheme.borderRadius['2xl']};\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n        }\n\n        .wallet-balance {\n          font-size: ${dattaAbleTheme.typography.fontSize['4xl']};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.bold};\n        }\n\n        .stat-card {\n          transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n\n        .stat-card:hover {\n          transform: translateY(-4px);\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n        }\n\n        /* Bootstrap Tab components */\n        .nav-tabs {\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .nav-tabs .nav-link {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n          background-color: transparent !important;\n          border: 1px solid transparent !important;\n        }\n\n        .nav-tabs .nav-link:hover {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        .nav-tabs .nav-link.active {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} ${dattaAbleTheme.colors.border} ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .tab-content {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-radius: 0 0 ${dattaAbleTheme.borderRadius.lg} ${dattaAbleTheme.borderRadius.lg} !important;\n        }\n\n        .tab-pane {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Specific styling for wallet tab content */\n        .tab-pane .card-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Override any remaining white backgrounds in tab content */\n        .tab-content .card-body,\n        .tab-pane .card-body,\n        .nav-tabs + .tab-content,\n        .card .tab-content,\n        .card .tab-pane {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Specific wallet tab styling */\n        .wallet-tabs .tab-content,\n        .wallet-tabs .tab-pane,\n        .wallet-tabs .card-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Modal and Dialog backgrounds */\n        .modal-content {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .modal-header {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .modal-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .modal-footer {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-top: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Dropdown menus */\n        .dropdown-menu {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .dropdown-item {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .dropdown-item:hover {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Table styling */\n        .table {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .table th {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .table td {\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* List group styling */\n        .list-group-item {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Pagination styling */\n        .pagination .page-link {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .pagination .page-link:hover {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .pagination .page-item.active .page-link {\n          background-color: ${dattaAbleTheme.colors.primary.main} !important;\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        /* Badge styling */\n        .badge {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Spinner and loading states */\n        .spinner-border {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        /* Toast notifications */\n        .toast {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .toast-header {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Progress bars */\n        .progress {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        /* Breadcrumb styling */\n        .breadcrumb {\n          background-color: transparent !important;\n        }\n\n        .breadcrumb-item a {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        .breadcrumb-item.active {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Input group styling */\n        .input-group-text {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Accordion styling */\n        .accordion-item {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .accordion-header button {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .accordion-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Offcanvas styling */\n        .offcanvas {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .offcanvas-header {\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Popover styling */\n        .popover {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .popover-header {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .popover-body {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Tooltip styling */\n        .tooltip-inner {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Force all white backgrounds to use theme colors */\n        [style*=\"background-color: white\"],\n        [style*=\"background-color: #ffffff\"],\n        [style*=\"background-color: #fff\"],\n        [style*=\"background: white\"],\n        [style*=\"background: #ffffff\"],\n        [style*=\"background: #fff\"] {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Ensure all containers and content areas use dark theme */\n        .container,\n        .container-fluid,\n        .row,\n        .col,\n        [class*=\"col-\"] {\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        /* Override Bootstrap's default white backgrounds */\n        .bg-white {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .bg-light {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        /* Ensure form elements use dark theme */\n        .form-select,\n        .form-check-input,\n        .form-range {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .form-select:focus,\n        .form-check-input:focus,\n        .form-range:focus {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25 !important;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(DattaAbleLayout, \"PDblC3vVykexKZXLbD547fTfYJo=\", false, function () {\n  return [useLocation];\n});\n_c = DattaAbleLayout;\nexport default DattaAbleLayout;\nvar _c;\n$RefreshReg$(_c, \"DattaAbleLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "useLocation", "Datta<PERSON>bleHeader", "DattaAbleSidebar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DattaAbleBreadcrumbs", "dattaAbleTheme", "jsxDEV", "_jsxDEV", "DattaAbleLayout", "children", "_s", "sidebarOpen", "setSidebarOpen", "sidebarCollapsed", "setSidebarCollapsed", "location", "pathname", "root", "document", "documentElement", "Object", "entries", "cssVariables", "for<PERSON>ach", "key", "value", "style", "setProperty", "toggleSidebar", "toggleSidebarCollapse", "layoutStyles", "minHeight", "backgroundColor", "colors", "background", "default", "fontFamily", "typography", "mainContentStyles", "marginLeft", "layout", "sidebar", "collapsedWidth", "width", "transition", "display", "flexDirection", "contentWrapperStyles", "flex", "padding", "spacing", "paddingTop", "header", "height", "isOpen", "isCollapsed", "onToggle", "onCollapse", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "window", "innerWidth", "onToggleSidebar", "onToggleSidebarCollapse", "fluid", "className", "position", "top", "left", "right", "bottom", "zIndex", "onClick", "text", "primary", "secondary", "light", "borderRadius", "full", "lg", "shadows", "sm", "border", "paper", "md", "fontWeight", "medium", "main", "dark", "fontSize", "bold", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DattaAbleLayout.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport { useLocation } from 'react-router-dom';\nimport Da<PERSON><PERSON>bleHeader from './DattaAbleHeader';\nimport DattaAbleSidebar from './DattaAbleSidebar';\nimport DattaAbleFooter from './DattaAbleFooter';\nimport DattaAbleBreadcrumbs from './DattaAbleBreadcrumbs';\n\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport '@fontsource/open-sans/300.css';\nimport '@fontsource/open-sans/400.css';\nimport '@fontsource/open-sans/500.css';\nimport '@fontsource/open-sans/600.css';\nimport '@fontsource/open-sans/700.css';\n\ninterface DattaAbleLayoutProps {\n  children: React.ReactNode;\n}\n\nconst DattaAbleLayout: React.FC<DattaAbleLayoutProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const location = useLocation();\n\n  // Close sidebar on route change (mobile)\n  useEffect(() => {\n    setSidebarOpen(false);\n  }, [location.pathname]);\n\n  // Apply CSS variables to document root\n  useEffect(() => {\n    const root = document.documentElement;\n    Object.entries(dattaAbleTheme.cssVariables).forEach(([key, value]) => {\n      root.style.setProperty(key, value);\n    });\n  }, []);\n\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  const toggleSidebarCollapse = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const layoutStyles = {\n    minHeight: '100vh',\n    backgroundColor: dattaAbleTheme.colors.background.default,\n    fontFamily: dattaAbleTheme.typography.fontFamily,\n  };\n\n  const mainContentStyles: React.CSSProperties = {\n    marginLeft: sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,\n    transition: 'margin-left 0.3s ease',\n    minHeight: '100vh',\n    display: 'flex',\n    flexDirection: 'column',\n  };\n\n  const contentWrapperStyles: React.CSSProperties = {\n    flex: 1,\n    padding: dattaAbleTheme.spacing[4],\n    paddingTop: `calc(${dattaAbleTheme.layout.header.height} + ${dattaAbleTheme.spacing[4]})`,\n  };\n\n  return (\n    <div style={layoutStyles}>\n      {/* Sidebar */}\n      <DattaAbleSidebar\n        isOpen={sidebarOpen}\n        isCollapsed={sidebarCollapsed}\n        onToggle={toggleSidebar}\n        onCollapse={toggleSidebarCollapse}\n      />\n\n      {/* Main Content Area */}\n      <div \n        style={{\n          ...mainContentStyles,\n          marginLeft: window.innerWidth < 768 ? 0 : (sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width)\n        }}\n      >\n        {/* Header */}\n        <DattaAbleHeader\n          onToggleSidebar={toggleSidebar}\n          onToggleSidebarCollapse={toggleSidebarCollapse}\n          sidebarCollapsed={sidebarCollapsed}\n        />\n\n        {/* Content Wrapper */}\n        <div style={contentWrapperStyles}>\n          {/* Breadcrumbs */}\n          <DattaAbleBreadcrumbs />\n\n          {/* Main Content */}\n          <Container fluid className=\"px-0\">\n            <Row>\n              <Col>\n                {children}\n              </Col>\n            </Row>\n          </Container>\n        </div>\n\n        {/* Footer */}\n        <DattaAbleFooter />\n      </div>\n\n\n\n      {/* Mobile Overlay */}\n      {sidebarOpen && window.innerWidth < 768 && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            zIndex: 1040,\n          }}\n          onClick={toggleSidebar}\n        />\n      )}\n\n      {/* Custom Styles */}\n      <style>{`\n        /* Global body styling for dark theme */\n        body {\n          background-color: ${dattaAbleTheme.colors.background.default} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Override any remaining white backgrounds */\n        * {\n          scrollbar-width: thin;\n          scrollbar-color: ${dattaAbleTheme.colors.text.secondary} ${dattaAbleTheme.colors.background.light};\n        }\n\n        /* Custom scrollbar */\n        ::-webkit-scrollbar {\n          width: 6px;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: ${dattaAbleTheme.colors.background.light};\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: ${dattaAbleTheme.colors.text.secondary};\n          border-radius: ${dattaAbleTheme.borderRadius.full};\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        /* Responsive adjustments */\n        @media (max-width: 767.98px) {\n          .main-content {\n            margin-left: 0 !important;\n          }\n        }\n\n        /* Animation classes */\n        .fade-in {\n          animation: fadeIn 0.3s ease-in;\n        }\n\n        @keyframes fadeIn {\n          from {\n            opacity: 0;\n            transform: translateY(10px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        /* Bootstrap overrides for Datta Able styling */\n        .card {\n          border-radius: ${dattaAbleTheme.borderRadius.lg};\n          box-shadow: ${dattaAbleTheme.shadows.sm};\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .card-header {\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .card-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .text-muted {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Material-UI component overrides */\n        .MuiPaper-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiCard-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiDialog-paper {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiTypography-root {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiTypography-body2 {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Stepper components */\n        .MuiStepper-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label.Mui-active {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label.Mui-completed {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Input and form components */\n        .MuiOutlinedInput-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiOutlinedInput-notchedOutline {\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .MuiInputLabel-root {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Alert components */\n        .MuiAlert-root {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Bootstrap Alert overrides */\n        .alert-info {\n          --bs-alert-bg: #6c757d !important; /* Gray background */\n          --bs-alert-border-color: #5a6268 !important;\n          --bs-alert-color: #ffffff !important;\n        }\n\n        .btn {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.medium};\n        }\n\n        .form-control {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .form-control:focus {\n          border-color: ${dattaAbleTheme.colors.primary.main};\n          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25;\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .form-control::placeholder {\n          color: ${dattaAbleTheme.colors.text.secondary};\n        }\n\n        /* Custom utility classes */\n        .text-primary-custom {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .bg-primary-custom {\n          background-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .border-primary-custom {\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .shadow-custom {\n          box-shadow: ${dattaAbleTheme.shadows.md} !important;\n        }\n\n        /* Wallet specific styling */\n        .wallet-card {\n          background: linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%);\n          color: white;\n          border-radius: ${dattaAbleTheme.borderRadius['2xl']};\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n        }\n\n        .wallet-balance {\n          font-size: ${dattaAbleTheme.typography.fontSize['4xl']};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.bold};\n        }\n\n        .stat-card {\n          transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n\n        .stat-card:hover {\n          transform: translateY(-4px);\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n        }\n\n        /* Bootstrap Tab components */\n        .nav-tabs {\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .nav-tabs .nav-link {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n          background-color: transparent !important;\n          border: 1px solid transparent !important;\n        }\n\n        .nav-tabs .nav-link:hover {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        .nav-tabs .nav-link.active {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} ${dattaAbleTheme.colors.border} ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .tab-content {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-radius: 0 0 ${dattaAbleTheme.borderRadius.lg} ${dattaAbleTheme.borderRadius.lg} !important;\n        }\n\n        .tab-pane {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Specific styling for wallet tab content */\n        .tab-pane .card-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Override any remaining white backgrounds in tab content */\n        .tab-content .card-body,\n        .tab-pane .card-body,\n        .nav-tabs + .tab-content,\n        .card .tab-content,\n        .card .tab-pane {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Specific wallet tab styling */\n        .wallet-tabs .tab-content,\n        .wallet-tabs .tab-pane,\n        .wallet-tabs .card-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Modal and Dialog backgrounds */\n        .modal-content {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .modal-header {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .modal-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .modal-footer {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-top: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Dropdown menus */\n        .dropdown-menu {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .dropdown-item {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .dropdown-item:hover {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Table styling */\n        .table {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .table th {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .table td {\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* List group styling */\n        .list-group-item {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Pagination styling */\n        .pagination .page-link {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .pagination .page-link:hover {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .pagination .page-item.active .page-link {\n          background-color: ${dattaAbleTheme.colors.primary.main} !important;\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        /* Badge styling */\n        .badge {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Spinner and loading states */\n        .spinner-border {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        /* Toast notifications */\n        .toast {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .toast-header {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Progress bars */\n        .progress {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        /* Breadcrumb styling */\n        .breadcrumb {\n          background-color: transparent !important;\n        }\n\n        .breadcrumb-item a {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        .breadcrumb-item.active {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Input group styling */\n        .input-group-text {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Accordion styling */\n        .accordion-item {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .accordion-header button {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .accordion-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Offcanvas styling */\n        .offcanvas {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .offcanvas-header {\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Popover styling */\n        .popover {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .popover-header {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .popover-body {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Tooltip styling */\n        .tooltip-inner {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Force all white backgrounds to use theme colors */\n        [style*=\"background-color: white\"],\n        [style*=\"background-color: #ffffff\"],\n        [style*=\"background-color: #fff\"],\n        [style*=\"background: white\"],\n        [style*=\"background: #ffffff\"],\n        [style*=\"background: #fff\"] {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Ensure all containers and content areas use dark theme */\n        .container,\n        .container-fluid,\n        .row,\n        .col,\n        [class*=\"col-\"] {\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        /* Override Bootstrap's default white backgrounds */\n        .bg-white {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .bg-light {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        /* Ensure form elements use dark theme */\n        .form-select,\n        .form-check-input,\n        .form-range {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .form-select:focus,\n        .form-check-input:focus,\n        .form-range:focus {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25 !important;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default DattaAbleLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,oBAAoB,MAAM,wBAAwB;AAEzD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAO,sCAAsC;AAC7C,OAAO,+BAA+B;AACtC,OAAO,+BAA+B;AACtC,OAAO,+BAA+B;AACtC,OAAO,+BAA+B;AACtC,OAAO,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMvC,MAAMC,eAA+C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMoB,QAAQ,GAAGf,WAAW,CAAC,CAAC;;EAE9B;EACAJ,SAAS,CAAC,MAAM;IACdgB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,CAACG,QAAQ,CAACC,QAAQ,CAAC,CAAC;;EAEvB;EACApB,SAAS,CAAC,MAAM;IACd,MAAMqB,IAAI,GAAGC,QAAQ,CAACC,eAAe;IACrCC,MAAM,CAACC,OAAO,CAAChB,cAAc,CAACiB,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MACpER,IAAI,CAACS,KAAK,CAACC,WAAW,CAACH,GAAG,EAAEC,KAAK,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1BhB,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;EAED,MAAMkB,qBAAqB,GAAGA,CAAA,KAAM;IAClCf,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,MAAMiB,YAAY,GAAG;IACnBC,SAAS,EAAE,OAAO;IAClBC,eAAe,EAAE3B,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACC,OAAO;IACzDC,UAAU,EAAE/B,cAAc,CAACgC,UAAU,CAACD;EACxC,CAAC;EAED,MAAME,iBAAsC,GAAG;IAC7CC,UAAU,EAAE1B,gBAAgB,GAAGR,cAAc,CAACmC,MAAM,CAACC,OAAO,CAACC,cAAc,GAAGrC,cAAc,CAACmC,MAAM,CAACC,OAAO,CAACE,KAAK;IACjHC,UAAU,EAAE,uBAAuB;IACnCb,SAAS,EAAE,OAAO;IAClBc,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE;EACjB,CAAC;EAED,MAAMC,oBAAyC,GAAG;IAChDC,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE5C,cAAc,CAAC6C,OAAO,CAAC,CAAC,CAAC;IAClCC,UAAU,EAAE,QAAQ9C,cAAc,CAACmC,MAAM,CAACY,MAAM,CAACC,MAAM,MAAMhD,cAAc,CAAC6C,OAAO,CAAC,CAAC,CAAC;EACxF,CAAC;EAED,oBACE3C,OAAA;IAAKmB,KAAK,EAAEI,YAAa;IAAArB,QAAA,gBAEvBF,OAAA,CAACL,gBAAgB;MACfoD,MAAM,EAAE3C,WAAY;MACpB4C,WAAW,EAAE1C,gBAAiB;MAC9B2C,QAAQ,EAAE5B,aAAc;MACxB6B,UAAU,EAAE5B;IAAsB;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAGFtD,OAAA;MACEmB,KAAK,EAAE;QACL,GAAGY,iBAAiB;QACpBC,UAAU,EAAEuB,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,CAAC,GAAIlD,gBAAgB,GAAGR,cAAc,CAACmC,MAAM,CAACC,OAAO,CAACC,cAAc,GAAGrC,cAAc,CAACmC,MAAM,CAACC,OAAO,CAACE;MAC7I,CAAE;MAAAlC,QAAA,gBAGFF,OAAA,CAACN,eAAe;QACd+D,eAAe,EAAEpC,aAAc;QAC/BqC,uBAAuB,EAAEpC,qBAAsB;QAC/ChB,gBAAgB,EAAEA;MAAiB;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAGFtD,OAAA;QAAKmB,KAAK,EAAEqB,oBAAqB;QAAAtC,QAAA,gBAE/BF,OAAA,CAACH,oBAAoB;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGxBtD,OAAA,CAACV,SAAS;UAACqE,KAAK;UAACC,SAAS,EAAC,MAAM;UAAA1D,QAAA,eAC/BF,OAAA,CAACT,GAAG;YAAAW,QAAA,eACFF,OAAA,CAACR,GAAG;cAAAU,QAAA,EACDA;YAAQ;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAGNtD,OAAA,CAACJ,eAAe;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAKLlD,WAAW,IAAImD,MAAM,CAACC,UAAU,GAAG,GAAG,iBACrCxD,OAAA;MACEmB,KAAK,EAAE;QACL0C,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTxC,eAAe,EAAE,oBAAoB;QACrCyC,MAAM,EAAE;MACV,CAAE;MACFC,OAAO,EAAE9C;IAAc;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF,eAGDtD,OAAA;MAAAE,QAAA,EAAQ;AACd;AACA;AACA,8BAA8BJ,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACC,OAAO;AACtE,mBAAmB9B,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA,6BAA6BvE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACE,SAAS,IAAIxE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBzE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AAC9D;AACA;AACA;AACA,wBAAwBzE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACE,SAAS;AAC5D,2BAA2BxE,cAAc,CAAC0E,YAAY,CAACC,IAAI;AAC3D;AACA;AACA;AACA,wBAAwB3E,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2BvE,cAAc,CAAC0E,YAAY,CAACE,EAAE;AACzD,wBAAwB5E,cAAc,CAAC6E,OAAO,CAACC,EAAE;AACjD,8BAA8B9E,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AAC1D,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,qCAAqChF,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACjE,mBAAmB/E,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA;AACA,8BAA8BxE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA;AACA,8BAA8BxE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE;AACA;AACA;AACA,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA,mBAAmBxE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,0BAA0BvE,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACtD;AACA;AACA;AACA,mBAAmB/E,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA;AACA,8BAA8BxE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE,mBAAmBzE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2BvE,cAAc,CAAC0E,YAAY,CAACO,EAAE;AACzD,yBAAyBjF,cAAc,CAACgC,UAAU,CAACkD,UAAU,CAACC,MAAM;AACpE;AACA;AACA;AACA,2BAA2BnF,cAAc,CAAC0E,YAAY,CAACO,EAAE;AACzD,8BAA8BjF,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AAC1D,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,0BAA0BvE,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AAC5D,qCAAqCpF,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AACvE,8BAA8BpF,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA;AACA,mBAAmBxE,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AACrD;AACA;AACA;AACA,8BAA8BpF,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AAChE;AACA;AACA;AACA,0BAA0BpF,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AAC5D;AACA;AACA;AACA,wBAAwBpF,cAAc,CAAC6E,OAAO,CAACI,EAAE;AACjD;AACA;AACA;AACA;AACA,gDAAgDjF,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI,QAAQpF,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACc,IAAI;AAC5H;AACA,2BAA2BrF,cAAc,CAAC0E,YAAY,CAAC,KAAK,CAAC;AAC7D,wBAAwB1E,cAAc,CAAC6E,OAAO,CAACD,EAAE;AACjD;AACA;AACA;AACA,uBAAuB5E,cAAc,CAACgC,UAAU,CAACsD,QAAQ,CAAC,KAAK,CAAC;AAChE,yBAAyBtF,cAAc,CAACgC,UAAU,CAACkD,UAAU,CAACK,IAAI;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBvF,cAAc,CAAC6E,OAAO,CAACD,EAAE;AACjD;AACA;AACA;AACA;AACA,qCAAqC5E,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACjE;AACA;AACA;AACA,mBAAmB/E,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA;AACA;AACA,mBAAmBxE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE;AACA;AACA;AACA,mBAAmBzE,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AACrD,8BAA8BpF,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,0BAA0BhF,cAAc,CAAC4B,MAAM,CAACmD,MAAM,IAAI/E,cAAc,CAAC4B,MAAM,CAACmD,MAAM,IAAI/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AAChI;AACA;AACA;AACA,8BAA8BhF,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,+BAA+BhF,cAAc,CAAC0E,YAAY,CAACE,EAAE,IAAI5E,cAAc,CAAC0E,YAAY,CAACE,EAAE;AAC/F;AACA;AACA;AACA,8BAA8B5E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,qCAAqChF,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACjE;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,kCAAkChF,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AAC9D;AACA;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,8BAA8BhF,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AAC1D;AACA;AACA;AACA,mBAAmB/E,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE,mBAAmBzE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE,mBAAmBzE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD,0BAA0BvE,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACtD;AACA;AACA;AACA,0BAA0B/E,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACtD;AACA;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD,0BAA0BvE,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACtD;AACA;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD,0BAA0BvE,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACtD;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE,mBAAmBzE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AAChE,0BAA0BpF,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AAC5D;AACA;AACA;AACA;AACA,8BAA8BpF,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE,mBAAmBzE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AACrD;AACA;AACA;AACA;AACA,8BAA8BpF,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AAC1D;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE,mBAAmBzE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD,qCAAqCvE,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACjE;AACA;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBzE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA,mBAAmBxE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE,mBAAmBzE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD,0BAA0BvE,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACtD;AACA;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,0BAA0BhF,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACtD;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,qCAAqCvE,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACjE;AACA;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,0BAA0BhF,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACtD;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE,mBAAmBzE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD,qCAAqCvE,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACjE;AACA;AACA;AACA,mBAAmB/E,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE;AACA;AACA;AACA,8BAA8BhF,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAAC4C,KAAK;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8BzE,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,mBAAmBhF,cAAc,CAAC4B,MAAM,CAAC0C,IAAI,CAACC,OAAO;AACrD,0BAA0BvE,cAAc,CAAC4B,MAAM,CAACmD,MAAM;AACtD;AACA;AACA;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC4B,MAAM,CAACC,UAAU,CAACmD,KAAK;AACpE,0BAA0BhF,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AAC5D,qCAAqCpF,cAAc,CAAC4B,MAAM,CAAC2C,OAAO,CAACa,IAAI;AACvE;AACA;IAAO;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnD,EAAA,CAhlBIF,eAA+C;EAAA,QAGlCR,WAAW;AAAA;AAAA6F,EAAA,GAHxBrF,eAA+C;AAklBrD,eAAeA,eAAe;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}