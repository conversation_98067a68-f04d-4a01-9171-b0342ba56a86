{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineUtilityClass(slot) {\n  return generateUtilityClass('MuiTimeline', slot);\n}\nconst timelineClasses = generateUtilityClasses('MuiTimeline', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse']);\nexport default timelineClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTimelineUtilityClass", "slot", "timelineClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/Timeline/timelineClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineUtilityClass(slot) {\n  return generateUtilityClass('MuiTimeline', slot);\n}\nconst timelineClasses = generateUtilityClasses('MuiTimeline', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'positionAlternateReverse']);\nexport default timelineClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,mBAAmB,EAAE,0BAA0B,CAAC,CAAC;AACzJ,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}