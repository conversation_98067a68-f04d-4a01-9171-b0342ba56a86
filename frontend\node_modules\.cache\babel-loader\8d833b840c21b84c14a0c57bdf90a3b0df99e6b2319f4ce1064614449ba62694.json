{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { getTabPanelUtilityClass } from \"./tabPanelClasses.js\";\nimport { getPanelId, getTabId, useTabContext } from \"../TabContext/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    hidden\n  } = ownerState;\n  const slots = {\n    root: ['root', hidden && 'hidden']\n  };\n  return composeClasses(slots, getTabPanelUtilityClass, classes);\n};\nconst TabPanelRoot = styled('div', {\n  name: 'MuiTabPanel',\n  slot: 'Root'\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    padding: theme.spacing(3)\n  };\n});\nconst TabPanel = /*#__PURE__*/React.forwardRef(function TabPanel(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTabPanel'\n  });\n  const {\n    children,\n    className,\n    value,\n    keepMounted = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props\n  };\n  const classes = useUtilityClasses(ownerState);\n  const context = useTabContext();\n  if (context === null) {\n    throw new TypeError('No TabContext provided');\n  }\n  const id = getPanelId(context, value);\n  const tabId = getTabId(context, value);\n  return /*#__PURE__*/_jsx(TabPanelRoot, {\n    \"aria-labelledby\": tabId,\n    className: clsx(classes.root, className),\n    hidden: value !== context.value,\n    id: id,\n    ref: ref,\n    role: \"tabpanel\",\n    ownerState: ownerState,\n    ...other,\n    children: (keepMounted || value === context.value) && children\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TabPanel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Always keep the children in the DOM.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The `value` of the corresponding `Tab`. Must use the index of the `Tab` when\n   * no `value` was passed to `Tab`.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired\n} : void 0;\nexport default TabPanel;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "styled", "useThemeProps", "composeClasses", "getTabPanelUtilityClass", "getPanelId", "getTabId", "useTabContext", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "hidden", "slots", "root", "TabPanelRoot", "name", "slot", "_ref", "theme", "padding", "spacing", "TabPanel", "forwardRef", "inProps", "ref", "props", "children", "className", "value", "keepMounted", "other", "context", "TypeError", "id", "tabId", "role", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "bool", "sx", "oneOfType", "arrayOf", "func", "number", "isRequired"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TabPanel/TabPanel.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { getTabPanelUtilityClass } from \"./tabPanelClasses.js\";\nimport { getPanelId, getTabId, useTabContext } from \"../TabContext/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    hidden\n  } = ownerState;\n  const slots = {\n    root: ['root', hidden && 'hidden']\n  };\n  return composeClasses(slots, getTabPanelUtilityClass, classes);\n};\nconst TabPanelRoot = styled('div', {\n  name: 'MuiTabPanel',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  padding: theme.spacing(3)\n}));\nconst TabPanel = /*#__PURE__*/React.forwardRef(function TabPanel(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTabPanel'\n  });\n  const {\n    children,\n    className,\n    value,\n    keepMounted = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props\n  };\n  const classes = useUtilityClasses(ownerState);\n  const context = useTabContext();\n  if (context === null) {\n    throw new TypeError('No TabContext provided');\n  }\n  const id = getPanelId(context, value);\n  const tabId = getTabId(context, value);\n  return /*#__PURE__*/_jsx(TabPanelRoot, {\n    \"aria-labelledby\": tabId,\n    className: clsx(classes.root, className),\n    hidden: value !== context.value,\n    id: id,\n    ref: ref,\n    role: \"tabpanel\",\n    ownerState: ownerState,\n    ...other,\n    children: (keepMounted || value === context.value) && children\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TabPanel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Always keep the children in the DOM.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The `value` of the corresponding `Tab`. Must use the index of the `Tab` when\n   * no `value` was passed to `Tab`.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired\n} : void 0;\nexport default TabPanel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,uBAAuB,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,wBAAwB;AAC5E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,MAAM,IAAI,QAAQ;EACnC,CAAC;EACD,OAAOV,cAAc,CAACW,KAAK,EAAEV,uBAAuB,EAAEQ,OAAO,CAAC;AAChE,CAAC;AACD,MAAMI,YAAY,GAAGf,MAAM,CAAC,KAAK,EAAE;EACjCgB,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE;AACR,CAAC,CAAC,CAACC,IAAA;EAAA,IAAC;IACFC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC;EAC1B,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,QAAQ,GAAG,aAAazB,KAAK,CAAC0B,UAAU,CAAC,SAASD,QAAQA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7E,MAAMC,KAAK,GAAGzB,aAAa,CAAC;IAC1ByB,KAAK,EAAEF,OAAO;IACdR,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJW,QAAQ;IACRC,SAAS;IACTC,KAAK;IACLC,WAAW,GAAG,KAAK;IACnB,GAAGC;EACL,CAAC,GAAGL,KAAK;EACT,MAAMhB,UAAU,GAAG;IACjB,GAAGgB;EACL,CAAC;EACD,MAAMf,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMsB,OAAO,GAAG1B,aAAa,CAAC,CAAC;EAC/B,IAAI0B,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC/C;EACA,MAAMC,EAAE,GAAG9B,UAAU,CAAC4B,OAAO,EAAEH,KAAK,CAAC;EACrC,MAAMM,KAAK,GAAG9B,QAAQ,CAAC2B,OAAO,EAAEH,KAAK,CAAC;EACtC,OAAO,aAAarB,IAAI,CAACO,YAAY,EAAE;IACrC,iBAAiB,EAAEoB,KAAK;IACxBP,SAAS,EAAE7B,IAAI,CAACY,OAAO,CAACG,IAAI,EAAEc,SAAS,CAAC;IACxChB,MAAM,EAAEiB,KAAK,KAAKG,OAAO,CAACH,KAAK;IAC/BK,EAAE,EAAEA,EAAE;IACNT,GAAG,EAAEA,GAAG;IACRW,IAAI,EAAE,UAAU;IAChB1B,UAAU,EAAEA,UAAU;IACtB,GAAGqB,KAAK;IACRJ,QAAQ,EAAE,CAACG,WAAW,IAAID,KAAK,KAAKG,OAAO,CAACH,KAAK,KAAKF;EACxD,CAAC,CAAC;AACJ,CAAC,CAAC;AACFU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjB,QAAQ,CAACkB,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;EACEb,QAAQ,EAAE7B,SAAS,CAAC2C,IAAI;EACxB;AACF;AACA;EACE9B,OAAO,EAAEb,SAAS,CAAC4C,MAAM;EACzB;AACF;AACA;EACEd,SAAS,EAAE9B,SAAS,CAAC6C,MAAM;EAC3B;AACF;AACA;AACA;EACEb,WAAW,EAAEhC,SAAS,CAAC8C,IAAI;EAC3B;AACF;AACA;EACEC,EAAE,EAAE/C,SAAS,CAACgD,SAAS,CAAC,CAAChD,SAAS,CAACiD,OAAO,CAACjD,SAAS,CAACgD,SAAS,CAAC,CAAChD,SAAS,CAACkD,IAAI,EAAElD,SAAS,CAAC4C,MAAM,EAAE5C,SAAS,CAAC8C,IAAI,CAAC,CAAC,CAAC,EAAE9C,SAAS,CAACkD,IAAI,EAAElD,SAAS,CAAC4C,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEb,KAAK,EAAE/B,SAAS,CAACgD,SAAS,CAAC,CAAChD,SAAS,CAACmD,MAAM,EAAEnD,SAAS,CAAC6C,MAAM,CAAC,CAAC,CAACO;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,eAAe5B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}