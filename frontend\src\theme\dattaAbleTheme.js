// Datta Able Theme Configuration
// This replaces the Material-UI theme with Bootstrap-based styling

// Color palette based on Datta Able design
export const colors = {
  primary: {
    main: '#1976d2',
    light: '#42a5f5',
    dark: '#1565c0',
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#dc004e',
    light: '#ff5983',
    dark: '#9a0036',
    contrastText: '#ffffff',
  },
  success: {
    main: '#4caf50',
    light: '#81c784',
    dark: '#388e3c',
    contrastText: '#ffffff',
  },
  warning: {
    main: '#ff9800',
    light: '#ffb74d',
    dark: '#f57c00',
    contrastText: '#ffffff',
  },
  error: {
    main: '#f44336',
    light: '#e57373',
    dark: '#d32f2f',
    contrastText: '#ffffff',
  },
  info: {
    main: '#2196f3',
    light: '#64b5f6',
    dark: '#1976d2',
    contrastText: '#ffffff',
  },
  background: {
    default: '#2d5016', // Changed to green
    paper: '#34495e',
    light: '#3a4a5c',
  },
  text: {
    primary: '#ffffff',
    secondary: '#ecf0f1',
    disabled: '#95a5a6',
  },
  divider: '#4a5568',
  border: '#4a5568',
};

// Typography configuration
export const typography = {
  fontFamily: '"Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
  },
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

// Spacing configuration (based on Bootstrap's spacing scale)
export const spacing = {
  0: '0',
  1: '0.25rem',  // 4px
  2: '0.5rem',   // 8px
  3: '0.75rem',  // 12px
  4: '1rem',     // 16px
  5: '1.25rem',  // 20px
  6: '1.5rem',   // 24px
  8: '2rem',     // 32px
  10: '2.5rem',  // 40px
  12: '3rem',    // 48px
  16: '4rem',    // 64px
  20: '5rem',    // 80px
  24: '6rem',    // 96px
};

// Border radius configuration
export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  base: '0.25rem',  // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px',
};

// Shadow configuration
export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
};

// Breakpoints (Bootstrap breakpoints)
export const breakpoints = {
  xs: '0px',
  sm: '576px',
  md: '768px',
  lg: '992px',
  xl: '1200px',
  xxl: '1400px',
};

// Layout configuration
export const layout = {
  sidebar: {
    width: '260px',
    collapsedWidth: '70px',
  },
  header: {
    height: '70px',
  },
  footer: {
    height: '60px',
  },
};

// Component styles
export const components = {
  card: {
    borderRadius: borderRadius.lg,
    boxShadow: shadows.sm,
    border: `1px solid ${colors.border}`,
  },
  button: {
    borderRadius: borderRadius.md,
    fontWeight: typography.fontWeight.medium,
    padding: {
      sm: `${spacing[2]} ${spacing[3]}`,
      md: `${spacing[3]} ${spacing[4]}`,
      lg: `${spacing[4]} ${spacing[6]}`,
    },
  },
  input: {
    borderRadius: borderRadius.md,
    border: `1px solid ${colors.border}`,
    padding: `${spacing[3]} ${spacing[4]}`,
  },
  modal: {
    borderRadius: borderRadius.lg,
    boxShadow: shadows.xl,
  },
};

// Utility functions
export const getColor = (colorPath) => {
  const keys = colorPath.split('.');
  let result = colors;
  for (const key of keys) {
    result = result[key];
    if (!result) return colors.primary.main; // fallback
  }
  return result;
};

export const getSpacing = (value) => {
  if (typeof value === 'number') {
    return spacing[value] || `${value * 0.25}rem`;
  }
  return spacing[value] || value;
};

// CSS custom properties for dynamic theming
export const cssVariables = {
  '--color-primary': colors.primary.main,
  '--color-primary-light': colors.primary.light,
  '--color-primary-dark': colors.primary.dark,
  '--color-secondary': colors.secondary.main,
  '--color-success': colors.success.main,
  '--color-warning': colors.warning.main,
  '--color-error': colors.error.main,
  '--color-info': colors.info.main,
  '--color-background': colors.background.default,
  '--color-paper': colors.background.paper,
  '--color-text-primary': colors.text.primary,
  '--color-text-secondary': colors.text.secondary,
  '--color-border': colors.border,
  '--font-family': typography.fontFamily,
  '--sidebar-width': layout.sidebar.width,
  '--header-height': layout.header.height,
  '--border-radius': borderRadius.md,
  '--shadow-sm': shadows.sm,
  '--shadow-md': shadows.md,
  '--shadow-lg': shadows.lg,
};

// Export default theme object
const dattaAbleTheme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  breakpoints,
  layout,
  components,
  cssVariables,
  getColor,
  getSpacing,
};

export default dattaAbleTheme;
