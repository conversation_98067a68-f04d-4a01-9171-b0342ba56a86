[{"C:\\laragon\\www\\frontend\\src\\index.tsx": "1", "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts": "2", "C:\\laragon\\www\\frontend\\src\\App.tsx": "3", "C:\\laragon\\www\\frontend\\src\\utils\\errorHandlers.ts": "4", "C:\\laragon\\www\\frontend\\src\\contexts\\SettingsContext.tsx": "5", "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx": "6", "C:\\laragon\\www\\frontend\\src\\components\\common\\DynamicHead.tsx": "7", "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "8", "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckEditor.tsx": "9", "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx": "10", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx": "11", "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx": "12", "C:\\laragon\\www\\frontend\\src\\components\\cms\\CmsHomepage.tsx": "13", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx": "14", "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx": "15", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx": "16", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx": "17", "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx": "18", "C:\\laragon\\www\\frontend\\src\\pages\\cms\\PageView.tsx": "19", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx": "20", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx": "21", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx": "22", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx": "23", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx": "24", "C:\\laragon\\www\\frontend\\src\\services\\settingsService.ts": "25", "C:\\laragon\\www\\frontend\\src\\services\\authService.ts": "26", "C:\\laragon\\www\\frontend\\src\\services\\cmsService.ts": "27", "C:\\laragon\\www\\frontend\\src\\components\\cms\\EditPageButton.tsx": "28", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleLayout.tsx": "29", "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckRenderer.tsx": "30", "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts": "31", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx": "32", "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts": "33", "C:\\laragon\\www\\frontend\\src\\components\\common\\Toast.tsx": "34", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileHistory.tsx": "35", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx": "36", "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx": "37", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileReUpload.tsx": "38", "C:\\laragon\\www\\frontend\\src\\theme\\dattaAbleTheme.js": "39", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTopUp.tsx": "40", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletBalance.tsx": "41", "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTransactionHistory.tsx": "42", "C:\\laragon\\www\\frontend\\src\\services\\api.ts": "43", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleFooter.tsx": "44", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleBreadcrumbs.tsx": "45", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleSidebar.tsx": "46", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleHeader.tsx": "47", "C:\\laragon\\www\\frontend\\src\\utils\\navigationCleanup.ts": "48", "C:\\laragon\\www\\frontend\\src\\utils\\navigationDiagnostics.ts": "49", "C:\\laragon\\www\\frontend\\src\\hooks\\useCleanNavigation.ts": "50", "C:\\laragon\\www\\frontend\\src\\utils\\strictModeWorkaround.ts": "51"}, {"size": 554, "mtime": 1752503120783, "results": "52", "hashOfConfig": "53"}, {"size": 425, "mtime": 1752503120799, "results": "54", "hashOfConfig": "53"}, {"size": 5109, "mtime": 1753230672127, "results": "55", "hashOfConfig": "53"}, {"size": 5218, "mtime": 1753280345117, "results": "56", "hashOfConfig": "53"}, {"size": 4306, "mtime": 1753137950997, "results": "57", "hashOfConfig": "53"}, {"size": 4264, "mtime": 1752969422664, "results": "58", "hashOfConfig": "53"}, {"size": 2270, "mtime": 1753105704198, "results": "59", "hashOfConfig": "53"}, {"size": 1355, "mtime": 1752503120861, "results": "60", "hashOfConfig": "53"}, {"size": 29109, "mtime": 1753281504260, "results": "61", "hashOfConfig": "53"}, {"size": 4076, "mtime": 1753105850909, "results": "62", "hashOfConfig": "53"}, {"size": 430, "mtime": 1752856061163, "results": "63", "hashOfConfig": "53"}, {"size": 3494, "mtime": 1752503120861, "results": "64", "hashOfConfig": "53"}, {"size": 6996, "mtime": 1753281939860, "results": "65", "hashOfConfig": "53"}, {"size": 5805, "mtime": 1752503120814, "results": "66", "hashOfConfig": "53"}, {"size": 4077, "mtime": 1752504117348, "results": "67", "hashOfConfig": "53"}, {"size": 3447, "mtime": 1752503120814, "results": "68", "hashOfConfig": "53"}, {"size": 4586, "mtime": 1752969325386, "results": "69", "hashOfConfig": "53"}, {"size": 11533, "mtime": 1752504965088, "results": "70", "hashOfConfig": "53"}, {"size": 4388, "mtime": 1753199936452, "results": "71", "hashOfConfig": "53"}, {"size": 7895, "mtime": 1752969474297, "results": "72", "hashOfConfig": "53"}, {"size": 10900, "mtime": 1753144568194, "results": "73", "hashOfConfig": "53"}, {"size": 50676, "mtime": 1753057376603, "results": "74", "hashOfConfig": "53"}, {"size": 41159, "mtime": 1753055376399, "results": "75", "hashOfConfig": "53"}, {"size": 7813, "mtime": 1753312885093, "results": "76", "hashOfConfig": "53"}, {"size": 5490, "mtime": 1753137950932, "results": "77", "hashOfConfig": "53"}, {"size": 6824, "mtime": 1753201331566, "results": "78", "hashOfConfig": "53"}, {"size": 11200, "mtime": 1753281576186, "results": "79", "hashOfConfig": "53"}, {"size": 4175, "mtime": 1753225676247, "results": "80", "hashOfConfig": "53"}, {"size": 20352, "mtime": 1753313996798, "results": "81", "hashOfConfig": "53"}, {"size": 3884, "mtime": 1753199110606, "results": "82", "hashOfConfig": "53"}, {"size": 12488, "mtime": 1753024608606, "results": "83", "hashOfConfig": "53"}, {"size": 4729, "mtime": 1752536669944, "results": "84", "hashOfConfig": "53"}, {"size": 5582, "mtime": 1752847027187, "results": "85", "hashOfConfig": "53"}, {"size": 675, "mtime": 1753025888572, "results": "86", "hashOfConfig": "53"}, {"size": 8243, "mtime": 1753051474206, "results": "87", "hashOfConfig": "53"}, {"size": 29255, "mtime": 1753018297878, "results": "88", "hashOfConfig": "53"}, {"size": 1430, "mtime": 1752673572781, "results": "89", "hashOfConfig": "53"}, {"size": 10466, "mtime": 1753053921568, "results": "90", "hashOfConfig": "53"}, {"size": 5454, "mtime": 1753313973202, "results": "91", "hashOfConfig": "53"}, {"size": 11687, "mtime": 1752988950816, "results": "92", "hashOfConfig": "53"}, {"size": 11644, "mtime": 1752856424457, "results": "93", "hashOfConfig": "53"}, {"size": 10996, "mtime": 1752856638130, "results": "94", "hashOfConfig": "53"}, {"size": 2311, "mtime": 1753227507762, "results": "95", "hashOfConfig": "53"}, {"size": 2939, "mtime": 1752883952010, "results": "96", "hashOfConfig": "53"}, {"size": 2706, "mtime": 1752922816616, "results": "97", "hashOfConfig": "53"}, {"size": 7938, "mtime": 1753105982975, "results": "98", "hashOfConfig": "53"}, {"size": 10352, "mtime": 1752970126996, "results": "99", "hashOfConfig": "53"}, {"size": 5193, "mtime": 1753017078083, "results": "100", "hashOfConfig": "53"}, {"size": 7310, "mtime": 1753017533626, "results": "101", "hashOfConfig": "53"}, {"size": 3019, "mtime": 1753017058753, "results": "102", "hashOfConfig": "53"}, {"size": 2543, "mtime": 1753003973465, "results": "103", "hashOfConfig": "53"}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tace3p", {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\frontend\\src\\index.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\laragon\\www\\frontend\\src\\App.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\errorHandlers.ts", [], [], "C:\\laragon\\www\\frontend\\src\\contexts\\SettingsContext.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\DynamicHead.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckEditor.tsx", ["257"], [], "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\cms\\CmsHomepage.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\cms\\PageView.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx", ["258", "259", "260", "261", "262", "263", "264", "265", "266", "267", "268", "269"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx", ["270", "271", "272", "273", "274", "275"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\settingsService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\services\\authService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\services\\cmsService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\components\\cms\\EditPageButton.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleLayout.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\puck\\PuckRenderer.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts", ["276"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts", ["277", "278"], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\Toast.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileHistory.tsx", ["279"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx", ["280", "281"], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileReUpload.tsx", ["282"], [], "C:\\laragon\\www\\frontend\\src\\theme\\dattaAbleTheme.js", [], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTopUp.tsx", ["283", "284", "285"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletBalance.tsx", ["286"], [], "C:\\laragon\\www\\frontend\\src\\components\\wallet\\WalletTransactionHistory.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\api.ts", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleFooter.tsx", ["287", "288", "289", "290"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleBreadcrumbs.tsx", ["291", "292"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleSidebar.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DattaAbleHeader.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\navigationCleanup.ts", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\navigationDiagnostics.ts", [], [], "C:\\laragon\\www\\frontend\\src\\hooks\\useCleanNavigation.ts", [], [], "C:\\laragon\\www\\frontend\\src\\utils\\strictModeWorkaround.ts", ["293"], [], {"ruleId": "294", "severity": 1, "message": "295", "line": 675, "column": 6, "nodeType": "296", "endLine": 675, "endColumn": 14, "suggestions": "297"}, {"ruleId": "298", "severity": 1, "message": "299", "line": 27, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 27, "endColumn": 10}, {"ruleId": "298", "severity": 1, "message": "302", "line": 32, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 32, "endColumn": 19}, {"ruleId": "298", "severity": 1, "message": "303", "line": 40, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 40, "endColumn": 9}, {"ruleId": "298", "severity": 1, "message": "304", "line": 41, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 41, "endColumn": 10}, {"ruleId": "298", "severity": 1, "message": "305", "line": 42, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 42, "endColumn": 7}, {"ruleId": "298", "severity": 1, "message": "306", "line": 43, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 43, "endColumn": 6}, {"ruleId": "298", "severity": 1, "message": "307", "line": 44, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 44, "endColumn": 8}, {"ruleId": "298", "severity": 1, "message": "308", "line": 53, "column": 17, "nodeType": "300", "messageId": "301", "endLine": 53, "endColumn": 27}, {"ruleId": "298", "severity": 1, "message": "309", "line": 79, "column": 9, "nodeType": "300", "messageId": "301", "endLine": 79, "endColumn": 17}, {"ruleId": "298", "severity": 1, "message": "310", "line": 104, "column": 10, "nodeType": "300", "messageId": "301", "endLine": 104, "endColumn": 19}, {"ruleId": "298", "severity": 1, "message": "311", "line": 104, "column": 21, "nodeType": "300", "messageId": "301", "endLine": 104, "endColumn": 33}, {"ruleId": "294", "severity": 1, "message": "312", "line": 114, "column": 6, "nodeType": "296", "endLine": 114, "endColumn": 12, "suggestions": "313"}, {"ruleId": "298", "severity": 1, "message": "314", "line": 18, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 18, "endColumn": 14}, {"ruleId": "298", "severity": 1, "message": "315", "line": 19, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 19, "endColumn": 13}, {"ruleId": "298", "severity": 1, "message": "316", "line": 20, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 20, "endColumn": 9}, {"ruleId": "298", "severity": 1, "message": "317", "line": 21, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 21, "endColumn": 11}, {"ruleId": "298", "severity": 1, "message": "318", "line": 48, "column": 25, "nodeType": "300", "messageId": "301", "endLine": 48, "endColumn": 41}, {"ruleId": "298", "severity": 1, "message": "319", "line": 58, "column": 10, "nodeType": "300", "messageId": "301", "endLine": 58, "endColumn": 22}, {"ruleId": "320", "severity": 1, "message": "321", "line": 413, "column": 1, "nodeType": "322", "endLine": 413, "endColumn": 38}, {"ruleId": "298", "severity": 1, "message": "323", "line": 1, "column": 15, "nodeType": "300", "messageId": "301", "endLine": 1, "endColumn": 24}, {"ruleId": "320", "severity": 1, "message": "321", "line": 223, "column": 1, "nodeType": "322", "endLine": 223, "endColumn": 36}, {"ruleId": "294", "severity": 1, "message": "324", "line": 58, "column": 6, "nodeType": "296", "endLine": 58, "endColumn": 30, "suggestions": "325"}, {"ruleId": "294", "severity": 1, "message": "326", "line": 91, "column": 6, "nodeType": "296", "endLine": 91, "endColumn": 15, "suggestions": "327"}, {"ruleId": "294", "severity": 1, "message": "328", "line": 280, "column": 6, "nodeType": "296", "endLine": 280, "endColumn": 49, "suggestions": "329"}, {"ruleId": "298", "severity": 1, "message": "330", "line": 48, "column": 10, "nodeType": "300", "messageId": "301", "endLine": 48, "endColumn": 20}, {"ruleId": "298", "severity": 1, "message": "331", "line": 3, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 3, "endColumn": 12}, {"ruleId": "298", "severity": 1, "message": "307", "line": 11, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 11, "endColumn": 8}, {"ruleId": "298", "severity": 1, "message": "332", "line": 111, "column": 9, "nodeType": "300", "messageId": "301", "endLine": 111, "endColumn": 19}, {"ruleId": "298", "severity": 1, "message": "331", "line": 3, "column": 3, "nodeType": "300", "messageId": "301", "endLine": 3, "endColumn": 12}, {"ruleId": "333", "severity": 1, "message": "334", "line": 33, "column": 15, "nodeType": "335", "endLine": 42, "endColumn": 16}, {"ruleId": "333", "severity": 1, "message": "334", "line": 49, "column": 15, "nodeType": "335", "endLine": 59, "endColumn": 16}, {"ruleId": "333", "severity": 1, "message": "334", "line": 62, "column": 15, "nodeType": "335", "endLine": 72, "endColumn": 16}, {"ruleId": "333", "severity": 1, "message": "334", "line": 75, "column": 15, "nodeType": "335", "endLine": 84, "endColumn": 16}, {"ruleId": "298", "severity": 1, "message": "336", "line": 19, "column": 11, "nodeType": "300", "messageId": "301", "endLine": 19, "endColumn": 23}, {"ruleId": "298", "severity": 1, "message": "337", "line": 51, "column": 9, "nodeType": "300", "messageId": "301", "endLine": 51, "endColumn": 29}, {"ruleId": "320", "severity": 1, "message": "338", "line": 91, "column": 1, "nodeType": "322", "endLine": 97, "endColumn": 3}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkAdminAndLoadPage'. Either include it or remove the dependency array.", "ArrayExpression", ["339"], "@typescript-eslint/no-unused-vars", "'Divider' is defined but never used.", "Identifier", "unusedVar", "'CircularProgress' is defined but never used.", "'AppBar' is defined but never used.", "'Toolbar' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Badge' is defined but never used.", "'FilterIcon' is defined but never used.", "'isTablet' is assigned a value but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadOrders'. Either include it or remove the dependency array.", ["340"], "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'setCreatingOrder' is assigned a value but never used.", "'createdOrder' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "'endpoints' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadHistory'. Either include it or remove the dependency array.", ["341"], "React Hook useEffect has missing dependencies: 'loadSettings' and 'loadUploadedFiles'. Either include them or remove the dependency array.", ["342"], "React Hook useCallback has missing dependencies: 'uploadFiles' and 'validateFiles'. Either include them or remove the dependency array.", ["343"], "'dragActive' is assigned a value but never used.", "'Container' is defined but never used.", "'newBalance' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'pathSegments' is assigned a value but never used.", "'breadcrumbItemStyles' is assigned a value but never used.", "Assign object to a variable before exporting as module default", {"desc": "344", "fix": "345"}, {"desc": "346", "fix": "347"}, {"desc": "348", "fix": "349"}, {"desc": "350", "fix": "351"}, {"desc": "352", "fix": "353"}, "Update the dependencies array to be: [checkAdminAndLoadPage, pageId]", {"range": "354", "text": "355"}, "Update the dependencies array to be: [loadOrders, page]", {"range": "356", "text": "357"}, "Update the dependencies array to be: [open, orderId, file.id, loadHistory]", {"range": "358", "text": "359"}, "Update the dependencies array to be: [loadSettings, loadUploadedFiles, orderId]", {"range": "360", "text": "361"}, "Update the dependencies array to be: [orderId, validateFiles, onError, uploadFiles]", {"range": "362", "text": "363"}, [22963, 22971], "[checkAdminAndLoadPage, pageId]", [3415, 3421], "[loadOrders, page]", [1158, 1182], "[open, orderId, file.id, loadHistory]", [2685, 2694], "[loadSettings, loadUploadedFiles, orderId]", [9503, 9546], "[orderId, validateFiles, onError, uploadFiles]"]