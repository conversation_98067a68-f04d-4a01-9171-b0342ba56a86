{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The YearPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { YearPicker } from '@mui/x-date-pickers'`\", \"or `import { YearPicker } from '@mui/x-date-pickers/YearPicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The YearPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst YearPicker = function DeprecatedYearPicker() {\n  warn();\n  return null;\n};\nexport default YearPicker;\nexport const yearPickerClasses = {};\nexport const getYearPickerUtilityClass = slot => {\n  warn();\n  return '';\n};", "map": {"version": 3, "names": ["warnedOnce", "warn", "console", "join", "YearPicker", "DeprecatedYearPicker", "yearPickerClasses", "getYearPickerUtilityClass", "slot"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/YearPicker/YearPicker.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The YearPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { YearPicker } from '@mui/x-date-pickers'`\", \"or `import { YearPicker } from '@mui/x-date-pickers/YearPicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The YearPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst YearPicker = function DeprecatedYearPicker() {\n  warn();\n  return null;\n};\nexport default YearPicker;\nexport const yearPickerClasses = {};\nexport const getYearPickerUtilityClass = slot => {\n  warn();\n  return '';\n};"], "mappings": "AAAA,YAAY;;AAEZ;AACA,IAAIA,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,mFAAmF,EAAE,EAAE,EAAE,mEAAmE,EAAE,kEAAkE,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IACtWH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,UAAU,GAAG,SAASC,oBAAoBA,CAAA,EAAG;EACjDJ,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC;AACD,eAAeG,UAAU;AACzB,OAAO,MAAME,iBAAiB,GAAG,CAAC,CAAC;AACnC,OAAO,MAAMC,yBAAyB,GAAGC,IAAI,IAAI;EAC/CP,IAAI,CAAC,CAAC;EACN,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}