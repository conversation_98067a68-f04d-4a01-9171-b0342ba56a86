{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from \"../Timeline/TimelineContext.js\";\nimport { getTimelineContentUtilityClass } from \"./timelineContentClasses.js\";\nimport convertTimelinePositionToClass from \"../internal/convertTimelinePositionToClass.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineContentUtilityClass, classes);\n};\nconst TimelineContentRoot = styled(Typography, {\n  name: 'MuiTimelineContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return {\n    flex: 1,\n    padding: '6px 16px',\n    textAlign: 'left',\n    ...(ownerState.position === 'left' && {\n      textAlign: 'right'\n    })\n  };\n});\nconst TimelineContent = /*#__PURE__*/React.forwardRef(function TimelineContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineContent'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = {\n    ...props,\n    position: positionContext || 'right'\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineContentRoot, {\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineContent;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "styled", "useThemeProps", "composeClasses", "Typography", "TimelineContext", "getTimelineContentUtilityClass", "convertTimelinePositionToClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "position", "classes", "slots", "root", "TimelineContentRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "flex", "padding", "textAlign", "TimelineContent", "forwardRef", "inProps", "ref", "className", "other", "positionContext", "useContext", "component", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TimelineContent/TimelineContent.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography from '@mui/material/Typography';\nimport TimelineContext from \"../Timeline/TimelineContext.js\";\nimport { getTimelineContentUtilityClass } from \"./timelineContentClasses.js\";\nimport convertTimelinePositionToClass from \"../internal/convertTimelinePositionToClass.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position)]\n  };\n  return composeClasses(slots, getTimelineContentUtilityClass, classes);\n};\nconst TimelineContentRoot = styled(Typography, {\n  name: 'MuiTimelineContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(({\n  ownerState\n}) => ({\n  flex: 1,\n  padding: '6px 16px',\n  textAlign: 'left',\n  ...(ownerState.position === 'left' && {\n    textAlign: 'right'\n  })\n}));\nconst TimelineContent = /*#__PURE__*/React.forwardRef(function TimelineContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineContent'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  const ownerState = {\n    ...props,\n    position: positionContext || 'right'\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TimelineContentRoot, {\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,SAASC,8BAA8B,QAAQ,6BAA6B;AAC5E,OAAOC,8BAA8B,MAAM,+CAA+C;AAC1F,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAER,8BAA8B,CAACK,QAAQ,CAAC;EACzD,CAAC;EACD,OAAOT,cAAc,CAACW,KAAK,EAAER,8BAA8B,EAAEO,OAAO,CAAC;AACvE,CAAC;AACD,MAAMG,mBAAmB,GAAGf,MAAM,CAACG,UAAU,EAAE;EAC7Ca,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACd,8BAA8B,CAACI,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC;EACnF;AACF,CAAC,CAAC,CAACU,IAAA;EAAA,IAAC;IACFX;EACF,CAAC,GAAAW,IAAA;EAAA,OAAM;IACLC,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,MAAM;IACjB,IAAId,UAAU,CAACC,QAAQ,KAAK,MAAM,IAAI;MACpCa,SAAS,EAAE;IACb,CAAC;EACH,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,eAAe,GAAG,aAAa5B,KAAK,CAAC6B,UAAU,CAAC,SAASD,eAAeA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3F,MAAMT,KAAK,GAAGlB,aAAa,CAAC;IAC1BkB,KAAK,EAAEQ,OAAO;IACdX,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJa,SAAS;IACT,GAAGC;EACL,CAAC,GAAGX,KAAK;EACT,MAAM;IACJR,QAAQ,EAAEoB;EACZ,CAAC,GAAGlC,KAAK,CAACmC,UAAU,CAAC5B,eAAe,CAAC;EACrC,MAAMM,UAAU,GAAG;IACjB,GAAGS,KAAK;IACRR,QAAQ,EAAEoB,eAAe,IAAI;EAC/B,CAAC;EACD,MAAMnB,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,mBAAmB,EAAE;IAC5CkB,SAAS,EAAE,KAAK;IAChBJ,SAAS,EAAE9B,IAAI,CAACa,OAAO,CAACE,IAAI,EAAEe,SAAS,CAAC;IACxCnB,UAAU,EAAEA,UAAU;IACtBkB,GAAG,EAAEA,GAAG;IACR,GAAGE;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,eAAe,CAACY,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAExC,SAAS,CAACyC,IAAI;EACxB;AACF;AACA;EACE3B,OAAO,EAAEd,SAAS,CAAC0C,MAAM;EACzB;AACF;AACA;EACEX,SAAS,EAAE/B,SAAS,CAAC2C,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAE5C,SAAS,CAAC6C,SAAS,CAAC,CAAC7C,SAAS,CAAC8C,OAAO,CAAC9C,SAAS,CAAC6C,SAAS,CAAC,CAAC7C,SAAS,CAAC+C,IAAI,EAAE/C,SAAS,CAAC0C,MAAM,EAAE1C,SAAS,CAACgD,IAAI,CAAC,CAAC,CAAC,EAAEhD,SAAS,CAAC+C,IAAI,EAAE/C,SAAS,CAAC0C,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAef,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}