{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTabPanelUtilityClass(slot) {\n  return generateUtilityClass('MuiTabPanel', slot);\n}\nconst tabPanelClasses = generateUtilityClasses('MuiTabPanel', ['root', 'hidden']);\nexport default tabPanelClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabPanelUtilityClass", "slot", "tabPanelClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TabPanel/tabPanelClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTabPanelUtilityClass(slot) {\n  return generateUtilityClass('MuiTabPanel', slot);\n}\nconst tabPanelClasses = generateUtilityClasses('MuiTabPanel', ['root', 'hidden']);\nexport default tabPanelClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACjF,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}