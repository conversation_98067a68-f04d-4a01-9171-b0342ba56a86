import React, { useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { useLocation } from 'react-router-dom';
import Da<PERSON><PERSON>bleHeader from './DattaAbleHeader';
import DattaAbleSidebar from './DattaAbleSidebar';
import DattaAbleFooter from './DattaAbleFooter';
import DattaAbleBreadcrumbs from './DattaAbleBreadcrumbs';

import dattaAbleTheme from '../../theme/dattaAbleTheme';
import 'bootstrap/dist/css/bootstrap.min.css';
import '@fontsource/open-sans/300.css';
import '@fontsource/open-sans/400.css';
import '@fontsource/open-sans/500.css';
import '@fontsource/open-sans/600.css';
import '@fontsource/open-sans/700.css';

interface DattaAbleLayoutProps {
  children: React.ReactNode;
}

const DattaAbleLayout: React.FC<DattaAbleLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const location = useLocation();

  // Close sidebar on route change (mobile)
  useEffect(() => {
    setSidebarOpen(false);
  }, [location.pathname]);

  // Apply CSS variables to document root
  useEffect(() => {
    const root = document.documentElement;
    Object.entries(dattaAbleTheme.cssVariables).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const toggleSidebarCollapse = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const layoutStyles = {
    minHeight: '100vh',
    backgroundColor: dattaAbleTheme.colors.background.default,
    fontFamily: dattaAbleTheme.typography.fontFamily,
  };

  const mainContentStyles: React.CSSProperties = {
    marginLeft: sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,
    transition: 'margin-left 0.3s ease',
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
  };

  const contentWrapperStyles: React.CSSProperties = {
    flex: 1,
    padding: dattaAbleTheme.spacing[4],
    paddingTop: `calc(${dattaAbleTheme.layout.header.height} + ${dattaAbleTheme.spacing[4]})`,
  };

  return (
    <div style={layoutStyles}>
      {/* Sidebar */}
      <DattaAbleSidebar
        isOpen={sidebarOpen}
        isCollapsed={sidebarCollapsed}
        onToggle={toggleSidebar}
        onCollapse={toggleSidebarCollapse}
      />

      {/* Main Content Area */}
      <div 
        style={{
          ...mainContentStyles,
          marginLeft: window.innerWidth < 768 ? 0 : (sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width)
        }}
      >
        {/* Header */}
        <DattaAbleHeader
          onToggleSidebar={toggleSidebar}
          onToggleSidebarCollapse={toggleSidebarCollapse}
          sidebarCollapsed={sidebarCollapsed}
        />

        {/* Content Wrapper */}
        <div style={contentWrapperStyles}>
          {/* Breadcrumbs */}
          <DattaAbleBreadcrumbs />

          {/* Main Content */}
          <Container fluid className="px-0">
            <Row>
              <Col>
                {children}
              </Col>
            </Row>
          </Container>
        </div>

        {/* Footer */}
        <DattaAbleFooter />
      </div>



      {/* Mobile Overlay */}
      {sidebarOpen && window.innerWidth < 768 && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1040,
          }}
          onClick={toggleSidebar}
        />
      )}

      {/* Custom Styles */}
      <style>{`
        /* Global body styling for dark theme */
        body {
          background-color: ${dattaAbleTheme.colors.background.default} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Override any remaining white backgrounds */
        * {
          scrollbar-width: thin;
          scrollbar-color: ${dattaAbleTheme.colors.text.secondary} ${dattaAbleTheme.colors.background.light};
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
          width: 6px;
        }
        
        ::-webkit-scrollbar-track {
          background: ${dattaAbleTheme.colors.background.light};
        }
        
        ::-webkit-scrollbar-thumb {
          background: ${dattaAbleTheme.colors.text.secondary};
          border-radius: ${dattaAbleTheme.borderRadius.full};
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: ${dattaAbleTheme.colors.text.primary};
        }

        /* Responsive adjustments */
        @media (max-width: 767.98px) {
          .main-content {
            margin-left: 0 !important;
          }
        }

        /* Animation classes */
        .fade-in {
          animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        /* Bootstrap overrides for Datta Able styling */
        .card {
          border-radius: ${dattaAbleTheme.borderRadius.lg};
          box-shadow: ${dattaAbleTheme.shadows.sm};
          border: 1px solid ${dattaAbleTheme.colors.border};
          background-color: ${dattaAbleTheme.colors.background.paper};
          color: ${dattaAbleTheme.colors.text.primary};
        }

        .card-header {
          background-color: ${dattaAbleTheme.colors.background.paper};
          border-bottom: 1px solid ${dattaAbleTheme.colors.border};
          color: ${dattaAbleTheme.colors.text.primary};
        }

        .card-body {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .text-muted {
          color: ${dattaAbleTheme.colors.text.secondary} !important;
        }

        /* Material-UI component overrides */
        .MuiPaper-root {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .MuiCard-root {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .MuiDialog-paper {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .MuiTypography-root {
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .MuiTypography-body2 {
          color: ${dattaAbleTheme.colors.text.secondary} !important;
        }

        /* Stepper components */
        .MuiStepper-root {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
        }

        .MuiStep-root .MuiStepLabel-label {
          color: ${dattaAbleTheme.colors.text.secondary} !important;
        }

        .MuiStep-root .MuiStepLabel-label.Mui-active {
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .MuiStep-root .MuiStepLabel-label.Mui-completed {
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Input and form components */
        .MuiOutlinedInput-root {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .MuiOutlinedInput-notchedOutline {
          border-color: ${dattaAbleTheme.colors.border} !important;
        }

        .MuiInputLabel-root {
          color: ${dattaAbleTheme.colors.text.secondary} !important;
        }

        /* Alert components */
        .MuiAlert-root {
          background-color: ${dattaAbleTheme.colors.background.light} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Bootstrap Alert overrides */
        .alert-info {
          --bs-alert-bg: #6c757d !important; /* Gray background */
          --bs-alert-border-color: #5a6268 !important;
          --bs-alert-color: #ffffff !important;
        }

        .btn {
          border-radius: ${dattaAbleTheme.borderRadius.md};
          font-weight: ${dattaAbleTheme.typography.fontWeight.medium};
        }

        .form-control {
          border-radius: ${dattaAbleTheme.borderRadius.md};
          border: 1px solid ${dattaAbleTheme.colors.border};
          background-color: ${dattaAbleTheme.colors.background.paper};
          color: ${dattaAbleTheme.colors.text.primary};
        }

        .form-control:focus {
          border-color: ${dattaAbleTheme.colors.primary.main};
          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25;
          background-color: ${dattaAbleTheme.colors.background.paper};
          color: ${dattaAbleTheme.colors.text.primary};
        }

        .form-control::placeholder {
          color: ${dattaAbleTheme.colors.text.secondary};
        }

        /* Custom utility classes */
        .text-primary-custom {
          color: ${dattaAbleTheme.colors.primary.main} !important;
        }

        .bg-primary-custom {
          background-color: ${dattaAbleTheme.colors.primary.main} !important;
        }

        .border-primary-custom {
          border-color: ${dattaAbleTheme.colors.primary.main} !important;
        }

        .shadow-custom {
          box-shadow: ${dattaAbleTheme.shadows.md} !important;
        }

        /* Wallet specific styling */
        .wallet-card {
          background: linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%);
          color: white;
          border-radius: ${dattaAbleTheme.borderRadius['2xl']};
          box-shadow: ${dattaAbleTheme.shadows.lg};
        }

        .wallet-balance {
          font-size: ${dattaAbleTheme.typography.fontSize['4xl']};
          font-weight: ${dattaAbleTheme.typography.fontWeight.bold};
        }

        .stat-card {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
          transform: translateY(-4px);
          box-shadow: ${dattaAbleTheme.shadows.lg};
        }

        /* Bootstrap Tab components */
        .nav-tabs {
          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;
        }

        .nav-tabs .nav-link {
          color: ${dattaAbleTheme.colors.text.secondary} !important;
          background-color: transparent !important;
          border: 1px solid transparent !important;
        }

        .nav-tabs .nav-link:hover {
          color: ${dattaAbleTheme.colors.text.primary} !important;
          background-color: ${dattaAbleTheme.colors.background.light} !important;
        }

        .nav-tabs .nav-link.active {
          color: ${dattaAbleTheme.colors.primary.main} !important;
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          border-color: ${dattaAbleTheme.colors.border} ${dattaAbleTheme.colors.border} ${dattaAbleTheme.colors.background.paper} !important;
        }

        .tab-content {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          border-radius: 0 0 ${dattaAbleTheme.borderRadius.lg} ${dattaAbleTheme.borderRadius.lg} !important;
        }

        .tab-pane {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Specific styling for wallet tab content */
        .tab-pane .card-body {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Override any remaining white backgrounds in tab content */
        .tab-content .card-body,
        .tab-pane .card-body,
        .nav-tabs + .tab-content,
        .card .tab-content,
        .card .tab-pane {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Specific wallet tab styling */
        .wallet-tabs .tab-content,
        .wallet-tabs .tab-pane,
        .wallet-tabs .card-body {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Modal and Dialog backgrounds */
        .modal-content {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .modal-header {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;
        }

        .modal-body {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .modal-footer {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          border-top: 1px solid ${dattaAbleTheme.colors.border} !important;
        }

        /* Dropdown menus */
        .dropdown-menu {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          border: 1px solid ${dattaAbleTheme.colors.border} !important;
        }

        .dropdown-item {
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .dropdown-item:hover {
          background-color: ${dattaAbleTheme.colors.background.light} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Table styling */
        .table {
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .table th {
          background-color: ${dattaAbleTheme.colors.background.light} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
          border-color: ${dattaAbleTheme.colors.border} !important;
        }

        .table td {
          border-color: ${dattaAbleTheme.colors.border} !important;
        }

        /* List group styling */
        .list-group-item {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
          border-color: ${dattaAbleTheme.colors.border} !important;
        }

        /* Pagination styling */
        .pagination .page-link {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
          border-color: ${dattaAbleTheme.colors.border} !important;
        }

        .pagination .page-link:hover {
          background-color: ${dattaAbleTheme.colors.background.light} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .pagination .page-item.active .page-link {
          background-color: ${dattaAbleTheme.colors.primary.main} !important;
          border-color: ${dattaAbleTheme.colors.primary.main} !important;
        }

        /* Badge styling */
        .badge {
          background-color: ${dattaAbleTheme.colors.background.light} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Spinner and loading states */
        .spinner-border {
          color: ${dattaAbleTheme.colors.primary.main} !important;
        }

        /* Toast notifications */
        .toast {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
          border: 1px solid ${dattaAbleTheme.colors.border} !important;
        }

        .toast-header {
          background-color: ${dattaAbleTheme.colors.background.light} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;
        }

        /* Progress bars */
        .progress {
          background-color: ${dattaAbleTheme.colors.background.light} !important;
        }

        /* Breadcrumb styling */
        .breadcrumb {
          background-color: transparent !important;
        }

        .breadcrumb-item a {
          color: ${dattaAbleTheme.colors.text.secondary} !important;
        }

        .breadcrumb-item.active {
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Input group styling */
        .input-group-text {
          background-color: ${dattaAbleTheme.colors.background.light} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
          border-color: ${dattaAbleTheme.colors.border} !important;
        }

        /* Accordion styling */
        .accordion-item {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          border-color: ${dattaAbleTheme.colors.border} !important;
        }

        .accordion-header button {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .accordion-body {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Offcanvas styling */
        .offcanvas {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        .offcanvas-header {
          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;
        }

        /* Popover styling */
        .popover {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          border-color: ${dattaAbleTheme.colors.border} !important;
        }

        .popover-header {
          background-color: ${dattaAbleTheme.colors.background.light} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;
        }

        .popover-body {
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Tooltip styling */
        .tooltip-inner {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Force all white backgrounds to use theme colors */
        [style*="background-color: white"],
        [style*="background-color: #ffffff"],
        [style*="background-color: #fff"],
        [style*="background: white"],
        [style*="background: #ffffff"],
        [style*="background: #fff"] {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
        }

        /* Ensure all containers and content areas use dark theme */
        .container,
        .container-fluid,
        .row,
        .col,
        [class*="col-"] {
          color: ${dattaAbleTheme.colors.text.primary};
        }

        /* Override Bootstrap's default white backgrounds */
        .bg-white {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
        }

        .bg-light {
          background-color: ${dattaAbleTheme.colors.background.light} !important;
        }

        /* Ensure form elements use dark theme */
        .form-select,
        .form-check-input,
        .form-range {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          color: ${dattaAbleTheme.colors.text.primary} !important;
          border-color: ${dattaAbleTheme.colors.border} !important;
        }

        .form-select:focus,
        .form-check-input:focus,
        .form-range:focus {
          background-color: ${dattaAbleTheme.colors.background.paper} !important;
          border-color: ${dattaAbleTheme.colors.primary.main} !important;
          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25 !important;
        }
      `}</style>
    </div>
  );
};

export default DattaAbleLayout;
