{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { isMuiElement } from '@mui/material/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { timelineContentClasses } from \"../TimelineContent/index.js\";\nimport { timelineOppositeContentClasses } from \"../TimelineOppositeContent/index.js\";\nimport TimelineContext from \"../Timeline/TimelineContext.js\";\nimport { getTimelineItemUtilityClass } from \"./timelineItemClasses.js\";\nimport convertTimelinePositionToClass from \"../internal/convertTimelinePositionToClass.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes,\n    hasOppositeContent\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position), !hasOppositeContent && 'missingOppositeContent']\n  };\n  return composeClasses(slots, getTimelineItemUtilityClass, classes);\n};\nconst TimelineItemRoot = styled('li', {\n  name: 'MuiTimelineItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return {\n    listStyle: 'none',\n    display: 'flex',\n    position: 'relative',\n    minHeight: 70,\n    ...(ownerState.position === 'left' && {\n      flexDirection: 'row-reverse'\n    }),\n    ...((ownerState.position === 'alternate' || ownerState.position === 'alternate-reverse') && {\n      [`&:nth-of-type(${ownerState.position === 'alternate' ? 'even' : 'odd'})`]: {\n        flexDirection: 'row-reverse',\n        [`& .${timelineContentClasses.root}`]: {\n          textAlign: 'right'\n        },\n        [`& .${timelineOppositeContentClasses.root}`]: {\n          textAlign: 'left'\n        }\n      }\n    }),\n    ...(!ownerState.hasOppositeContent && {\n      '&::before': {\n        content: '\"\"',\n        flex: 1,\n        padding: '6px 16px'\n      }\n    })\n  };\n});\nconst TimelineItem = /*#__PURE__*/React.forwardRef(function TimelineItem(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineItem'\n  });\n  const {\n    position: positionProp,\n    className,\n    ...other\n  } = props;\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  let hasOppositeContent = false;\n  React.Children.forEach(props.children, child => {\n    if (isMuiElement(child, ['TimelineOppositeContent'])) {\n      hasOppositeContent = true;\n    }\n  });\n  const ownerState = {\n    ...props,\n    position: positionProp || positionContext || 'right',\n    hasOppositeContent\n  };\n  const classes = useUtilityClasses(ownerState);\n  const contextValue = React.useMemo(() => ({\n    position: ownerState.position\n  }), [ownerState.position]);\n  return /*#__PURE__*/_jsx(TimelineContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(TimelineItemRoot, {\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ref: ref,\n      ...other\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The position where the timeline's item should appear.\n   */\n  position: PropTypes.oneOf(['alternate-reverse', 'alternate', 'left', 'right']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineItem;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "isMuiElement", "styled", "useThemeProps", "composeClasses", "timelineContentClasses", "timelineOppositeContentClasses", "TimelineContext", "getTimelineItemUtilityClass", "convertTimelinePositionToClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "position", "classes", "hasOppositeContent", "slots", "root", "TimelineItemRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "listStyle", "display", "minHeight", "flexDirection", "textAlign", "content", "flex", "padding", "TimelineItem", "forwardRef", "inProps", "ref", "positionProp", "className", "other", "positionContext", "useContext", "Children", "for<PERSON>ach", "children", "child", "contextValue", "useMemo", "Provider", "value", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "oneOf", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TimelineItem/TimelineItem.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { isMuiElement } from '@mui/material/utils';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { timelineContentClasses } from \"../TimelineContent/index.js\";\nimport { timelineOppositeContentClasses } from \"../TimelineOppositeContent/index.js\";\nimport TimelineContext from \"../Timeline/TimelineContext.js\";\nimport { getTimelineItemUtilityClass } from \"./timelineItemClasses.js\";\nimport convertTimelinePositionToClass from \"../internal/convertTimelinePositionToClass.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    position,\n    classes,\n    hasOppositeContent\n  } = ownerState;\n  const slots = {\n    root: ['root', convertTimelinePositionToClass(position), !hasOppositeContent && 'missingOppositeContent']\n  };\n  return composeClasses(slots, getTimelineItemUtilityClass, classes);\n};\nconst TimelineItemRoot = styled('li', {\n  name: 'MuiTimelineItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];\n  }\n})(({\n  ownerState\n}) => ({\n  listStyle: 'none',\n  display: 'flex',\n  position: 'relative',\n  minHeight: 70,\n  ...(ownerState.position === 'left' && {\n    flexDirection: 'row-reverse'\n  }),\n  ...((ownerState.position === 'alternate' || ownerState.position === 'alternate-reverse') && {\n    [`&:nth-of-type(${ownerState.position === 'alternate' ? 'even' : 'odd'})`]: {\n      flexDirection: 'row-reverse',\n      [`& .${timelineContentClasses.root}`]: {\n        textAlign: 'right'\n      },\n      [`& .${timelineOppositeContentClasses.root}`]: {\n        textAlign: 'left'\n      }\n    }\n  }),\n  ...(!ownerState.hasOppositeContent && {\n    '&::before': {\n      content: '\"\"',\n      flex: 1,\n      padding: '6px 16px'\n    }\n  })\n}));\nconst TimelineItem = /*#__PURE__*/React.forwardRef(function TimelineItem(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTimelineItem'\n  });\n  const {\n    position: positionProp,\n    className,\n    ...other\n  } = props;\n  const {\n    position: positionContext\n  } = React.useContext(TimelineContext);\n  let hasOppositeContent = false;\n  React.Children.forEach(props.children, child => {\n    if (isMuiElement(child, ['TimelineOppositeContent'])) {\n      hasOppositeContent = true;\n    }\n  });\n  const ownerState = {\n    ...props,\n    position: positionProp || positionContext || 'right',\n    hasOppositeContent\n  };\n  const classes = useUtilityClasses(ownerState);\n  const contextValue = React.useMemo(() => ({\n    position: ownerState.position\n  }), [ownerState.position]);\n  return /*#__PURE__*/_jsx(TimelineContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(TimelineItemRoot, {\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ref: ref,\n      ...other\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TimelineItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The position where the timeline's item should appear.\n   */\n  position: PropTypes.oneOf(['alternate-reverse', 'alternate', 'left', 'right']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TimelineItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,8BAA8B,QAAQ,qCAAqC;AACpF,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,SAASC,2BAA2B,QAAQ,0BAA0B;AACtE,OAAOC,8BAA8B,MAAM,+CAA+C;AAC1F,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC,OAAO;IACPC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAET,8BAA8B,CAACK,QAAQ,CAAC,EAAE,CAACE,kBAAkB,IAAI,wBAAwB;EAC1G,CAAC;EACD,OAAOZ,cAAc,CAACa,KAAK,EAAET,2BAA2B,EAAEO,OAAO,CAAC;AACpE,CAAC;AACD,MAAMI,gBAAgB,GAAGjB,MAAM,CAAC,IAAI,EAAE;EACpCkB,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJX;IACF,CAAC,GAAGU,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACf,8BAA8B,CAACI,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC;EACnF;AACF,CAAC,CAAC,CAACW,IAAA;EAAA,IAAC;IACFZ;EACF,CAAC,GAAAY,IAAA;EAAA,OAAM;IACLC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfb,QAAQ,EAAE,UAAU;IACpBc,SAAS,EAAE,EAAE;IACb,IAAIf,UAAU,CAACC,QAAQ,KAAK,MAAM,IAAI;MACpCe,aAAa,EAAE;IACjB,CAAC,CAAC;IACF,IAAI,CAAChB,UAAU,CAACC,QAAQ,KAAK,WAAW,IAAID,UAAU,CAACC,QAAQ,KAAK,mBAAmB,KAAK;MAC1F,CAAC,iBAAiBD,UAAU,CAACC,QAAQ,KAAK,WAAW,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG;QAC1Ee,aAAa,EAAE,aAAa;QAC5B,CAAC,MAAMxB,sBAAsB,CAACa,IAAI,EAAE,GAAG;UACrCY,SAAS,EAAE;QACb,CAAC;QACD,CAAC,MAAMxB,8BAA8B,CAACY,IAAI,EAAE,GAAG;UAC7CY,SAAS,EAAE;QACb;MACF;IACF,CAAC,CAAC;IACF,IAAI,CAACjB,UAAU,CAACG,kBAAkB,IAAI;MACpC,WAAW,EAAE;QACXe,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,CAAC;QACPC,OAAO,EAAE;MACX;IACF,CAAC;EACH,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,YAAY,GAAG,aAAapC,KAAK,CAACqC,UAAU,CAAC,SAASD,YAAYA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrF,MAAMd,KAAK,GAAGpB,aAAa,CAAC;IAC1BoB,KAAK,EAAEa,OAAO;IACdhB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJN,QAAQ,EAAEwB,YAAY;IACtBC,SAAS;IACT,GAAGC;EACL,CAAC,GAAGjB,KAAK;EACT,MAAM;IACJT,QAAQ,EAAE2B;EACZ,CAAC,GAAG3C,KAAK,CAAC4C,UAAU,CAACnC,eAAe,CAAC;EACrC,IAAIS,kBAAkB,GAAG,KAAK;EAC9BlB,KAAK,CAAC6C,QAAQ,CAACC,OAAO,CAACrB,KAAK,CAACsB,QAAQ,EAAEC,KAAK,IAAI;IAC9C,IAAI7C,YAAY,CAAC6C,KAAK,EAAE,CAAC,yBAAyB,CAAC,CAAC,EAAE;MACpD9B,kBAAkB,GAAG,IAAI;IAC3B;EACF,CAAC,CAAC;EACF,MAAMH,UAAU,GAAG;IACjB,GAAGU,KAAK;IACRT,QAAQ,EAAEwB,YAAY,IAAIG,eAAe,IAAI,OAAO;IACpDzB;EACF,CAAC;EACD,MAAMD,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMkC,YAAY,GAAGjD,KAAK,CAACkD,OAAO,CAAC,OAAO;IACxClC,QAAQ,EAAED,UAAU,CAACC;EACvB,CAAC,CAAC,EAAE,CAACD,UAAU,CAACC,QAAQ,CAAC,CAAC;EAC1B,OAAO,aAAaH,IAAI,CAACJ,eAAe,CAAC0C,QAAQ,EAAE;IACjDC,KAAK,EAAEH,YAAY;IACnBF,QAAQ,EAAE,aAAalC,IAAI,CAACQ,gBAAgB,EAAE;MAC5CoB,SAAS,EAAEvC,IAAI,CAACe,OAAO,CAACG,IAAI,EAAEqB,SAAS,CAAC;MACxC1B,UAAU,EAAEA,UAAU;MACtBwB,GAAG,EAAEA,GAAG;MACR,GAAGG;IACL,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnB,YAAY,CAACoB,SAAS,CAAC,yBAAyB;EACtF;EACA;EACA;EACA;EACA;AACF;AACA;EACET,QAAQ,EAAE9C,SAAS,CAACwD,IAAI;EACxB;AACF;AACA;EACExC,OAAO,EAAEhB,SAAS,CAACyD,MAAM;EACzB;AACF;AACA;EACEjB,SAAS,EAAExC,SAAS,CAAC0D,MAAM;EAC3B;AACF;AACA;EACE3C,QAAQ,EAAEf,SAAS,CAAC2D,KAAK,CAAC,CAAC,mBAAmB,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;EAC9E;AACF;AACA;EACEC,EAAE,EAAE5D,SAAS,CAAC6D,SAAS,CAAC,CAAC7D,SAAS,CAAC8D,OAAO,CAAC9D,SAAS,CAAC6D,SAAS,CAAC,CAAC7D,SAAS,CAAC+D,IAAI,EAAE/D,SAAS,CAACyD,MAAM,EAAEzD,SAAS,CAACgE,IAAI,CAAC,CAAC,CAAC,EAAEhE,SAAS,CAAC+D,IAAI,EAAE/D,SAAS,CAACyD,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAetB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}