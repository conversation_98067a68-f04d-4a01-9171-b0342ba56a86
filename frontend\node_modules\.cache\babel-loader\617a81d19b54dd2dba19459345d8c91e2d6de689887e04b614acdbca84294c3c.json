{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineConnectorUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineConnector', slot);\n}\nconst timelineConnectorClasses = generateUtilityClasses('MuiTimelineConnector', ['root']);\nexport default timelineConnectorClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTimelineConnectorUtilityClass", "slot", "timelineConnectorClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TimelineConnector/timelineConnectorClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineConnectorUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineConnector', slot);\n}\nconst timelineConnectorClasses = generateUtilityClasses('MuiTimelineConnector', ['root']);\nexport default timelineConnectorClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,gCAAgCA,CAACC,IAAI,EAAE;EACrD,OAAOH,oBAAoB,CAAC,sBAAsB,EAAEG,IAAI,CAAC;AAC3D;AACA,MAAMC,wBAAwB,GAAGH,sBAAsB,CAAC,sBAAsB,EAAE,CAAC,MAAM,CAAC,CAAC;AACzF,eAAeG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}