{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col}from'react-bootstrap';import{useLocation}from'react-router-dom';import Da<PERSON><PERSON>bleHeader from'./DattaAbleHeader';import DattaAbleSidebar from'./DattaAbleSidebar';import DattaAbleFooter from'./DattaAbleFooter';import DattaAbleBreadcrumbs from'./DattaAbleBreadcrumbs';import dattaAbleTheme from'../../theme/dattaAbleTheme';import'bootstrap/dist/css/bootstrap.min.css';import'@fontsource/open-sans/300.css';import'@fontsource/open-sans/400.css';import'@fontsource/open-sans/500.css';import'@fontsource/open-sans/600.css';import'@fontsource/open-sans/700.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DattaAbleLayout=_ref=>{let{children}=_ref;const[sidebarOpen,setSidebarOpen]=useState(false);const[sidebarCollapsed,setSidebarCollapsed]=useState(false);const location=useLocation();// Close sidebar on route change (mobile)\nuseEffect(()=>{setSidebarOpen(false);},[location.pathname]);// Apply CSS variables to document root\nuseEffect(()=>{const root=document.documentElement;Object.entries(dattaAbleTheme.cssVariables).forEach(_ref2=>{let[key,value]=_ref2;root.style.setProperty(key,value);});},[]);const toggleSidebar=()=>{setSidebarOpen(!sidebarOpen);};const toggleSidebarCollapse=()=>{setSidebarCollapsed(!sidebarCollapsed);};const layoutStyles={minHeight:'100vh',backgroundColor:dattaAbleTheme.colors.background.default,fontFamily:dattaAbleTheme.typography.fontFamily};const mainContentStyles={marginLeft:sidebarCollapsed?dattaAbleTheme.layout.sidebar.collapsedWidth:dattaAbleTheme.layout.sidebar.width,transition:'margin-left 0.3s ease',minHeight:'100vh',display:'flex',flexDirection:'column'};const contentWrapperStyles={flex:1,padding:dattaAbleTheme.spacing[4],paddingTop:`calc(${dattaAbleTheme.layout.header.height} + ${dattaAbleTheme.spacing[4]})`};return/*#__PURE__*/_jsxs(\"div\",{style:layoutStyles,children:[/*#__PURE__*/_jsx(DattaAbleSidebar,{isOpen:sidebarOpen,isCollapsed:sidebarCollapsed,onToggle:toggleSidebar,onCollapse:toggleSidebarCollapse}),/*#__PURE__*/_jsxs(\"div\",{style:{...mainContentStyles,marginLeft:window.innerWidth<768?0:sidebarCollapsed?dattaAbleTheme.layout.sidebar.collapsedWidth:dattaAbleTheme.layout.sidebar.width},children:[/*#__PURE__*/_jsx(DattaAbleHeader,{onToggleSidebar:toggleSidebar,onToggleSidebarCollapse:toggleSidebarCollapse,sidebarCollapsed:sidebarCollapsed}),/*#__PURE__*/_jsxs(\"div\",{style:contentWrapperStyles,children:[/*#__PURE__*/_jsx(DattaAbleBreadcrumbs,{}),/*#__PURE__*/_jsx(Container,{fluid:true,className:\"px-0\",children:/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:children})})})]}),/*#__PURE__*/_jsx(DattaAbleFooter,{})]}),sidebarOpen&&window.innerWidth<768&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.5)',zIndex:1040},onClick:toggleSidebar}),/*#__PURE__*/_jsx(\"style\",{children:`\n        /* Global body styling for dark theme */\n        body {\n          background-color: ${dattaAbleTheme.colors.background.default} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Override any remaining white backgrounds */\n        * {\n          scrollbar-width: thin;\n          scrollbar-color: ${dattaAbleTheme.colors.text.secondary} ${dattaAbleTheme.colors.background.light};\n        }\n\n        /* Custom scrollbar */\n        ::-webkit-scrollbar {\n          width: 6px;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: ${dattaAbleTheme.colors.background.light};\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: ${dattaAbleTheme.colors.text.secondary};\n          border-radius: ${dattaAbleTheme.borderRadius.full};\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        /* Responsive adjustments */\n        @media (max-width: 767.98px) {\n          .main-content {\n            margin-left: 0 !important;\n          }\n        }\n\n        /* Animation classes */\n        .fade-in {\n          animation: fadeIn 0.3s ease-in;\n        }\n\n        @keyframes fadeIn {\n          from {\n            opacity: 0;\n            transform: translateY(10px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        /* Bootstrap overrides for Datta Able styling */\n        .card {\n          border-radius: ${dattaAbleTheme.borderRadius.lg};\n          box-shadow: ${dattaAbleTheme.shadows.sm};\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .card-header {\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .card-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .text-muted {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Material-UI component overrides */\n        .MuiPaper-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiCard-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiDialog-paper {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiTypography-root {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiTypography-body2 {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Stepper components */\n        .MuiStepper-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label.Mui-active {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label.Mui-completed {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Input and form components */\n        .MuiOutlinedInput-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiOutlinedInput-notchedOutline {\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .MuiInputLabel-root {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Alert components */\n        .MuiAlert-root {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .btn {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.medium};\n        }\n\n        .form-control {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .form-control:focus {\n          border-color: ${dattaAbleTheme.colors.primary.main};\n          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25;\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .form-control::placeholder {\n          color: ${dattaAbleTheme.colors.text.secondary};\n        }\n\n        /* Custom utility classes */\n        .text-primary-custom {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .bg-primary-custom {\n          background-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .border-primary-custom {\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .shadow-custom {\n          box-shadow: ${dattaAbleTheme.shadows.md} !important;\n        }\n\n        /* Wallet specific styling */\n        .wallet-card {\n          background: linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%);\n          color: white;\n          border-radius: ${dattaAbleTheme.borderRadius['2xl']};\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n        }\n\n        .wallet-balance {\n          font-size: ${dattaAbleTheme.typography.fontSize['4xl']};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.bold};\n        }\n\n        .stat-card {\n          transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n\n        .stat-card:hover {\n          transform: translateY(-4px);\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n        }\n\n        /* Bootstrap Tab components */\n        .nav-tabs {\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .nav-tabs .nav-link {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n          background-color: transparent !important;\n          border: 1px solid transparent !important;\n        }\n\n        .nav-tabs .nav-link:hover {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        .nav-tabs .nav-link.active {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} ${dattaAbleTheme.colors.border} ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .tab-content {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-radius: 0 0 ${dattaAbleTheme.borderRadius.lg} ${dattaAbleTheme.borderRadius.lg} !important;\n        }\n\n        .tab-pane {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Specific styling for wallet tab content */\n        .tab-pane .card-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Override any remaining white backgrounds in tab content */\n        .tab-content .card-body,\n        .tab-pane .card-body,\n        .nav-tabs + .tab-content {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Modal and Dialog backgrounds */\n        .modal-content {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .modal-header {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .modal-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .modal-footer {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-top: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Dropdown menus */\n        .dropdown-menu {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .dropdown-item {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .dropdown-item:hover {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Table styling */\n        .table {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .table th {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .table td {\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* List group styling */\n        .list-group-item {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Pagination styling */\n        .pagination .page-link {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .pagination .page-link:hover {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .pagination .page-item.active .page-link {\n          background-color: ${dattaAbleTheme.colors.primary.main} !important;\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        /* Badge styling */\n        .badge {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Spinner and loading states */\n        .spinner-border {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        /* Toast notifications */\n        .toast {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .toast-header {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Progress bars */\n        .progress {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        /* Breadcrumb styling */\n        .breadcrumb {\n          background-color: transparent !important;\n        }\n\n        .breadcrumb-item a {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        .breadcrumb-item.active {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Input group styling */\n        .input-group-text {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Accordion styling */\n        .accordion-item {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .accordion-header button {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .accordion-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Offcanvas styling */\n        .offcanvas {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .offcanvas-header {\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Popover styling */\n        .popover {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .popover-header {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .popover-body {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Tooltip styling */\n        .tooltip-inner {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Force all white backgrounds to use theme colors */\n        [style*=\"background-color: white\"],\n        [style*=\"background-color: #ffffff\"],\n        [style*=\"background-color: #fff\"],\n        [style*=\"background: white\"],\n        [style*=\"background: #ffffff\"],\n        [style*=\"background: #fff\"] {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Ensure all containers and content areas use dark theme */\n        .container,\n        .container-fluid,\n        .row,\n        .col,\n        [class*=\"col-\"] {\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        /* Override Bootstrap's default white backgrounds */\n        .bg-white {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .bg-light {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        /* Ensure form elements use dark theme */\n        .form-select,\n        .form-check-input,\n        .form-range {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .form-select:focus,\n        .form-check-input:focus,\n        .form-range:focus {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25 !important;\n        }\n      `})]});};export default DattaAbleLayout;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "useLocation", "Datta<PERSON>bleHeader", "DattaAbleSidebar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DattaAbleBreadcrumbs", "dattaAbleTheme", "jsx", "_jsx", "jsxs", "_jsxs", "DattaAbleLayout", "_ref", "children", "sidebarOpen", "setSidebarOpen", "sidebarCollapsed", "setSidebarCollapsed", "location", "pathname", "root", "document", "documentElement", "Object", "entries", "cssVariables", "for<PERSON>ach", "_ref2", "key", "value", "style", "setProperty", "toggleSidebar", "toggleSidebarCollapse", "layoutStyles", "minHeight", "backgroundColor", "colors", "background", "default", "fontFamily", "typography", "mainContentStyles", "marginLeft", "layout", "sidebar", "collapsedWidth", "width", "transition", "display", "flexDirection", "contentWrapperStyles", "flex", "padding", "spacing", "paddingTop", "header", "height", "isOpen", "isCollapsed", "onToggle", "onCollapse", "window", "innerWidth", "onToggleSidebar", "onToggleSidebarCollapse", "fluid", "className", "position", "top", "left", "right", "bottom", "zIndex", "onClick", "text", "primary", "secondary", "light", "borderRadius", "full", "lg", "shadows", "sm", "border", "paper", "md", "fontWeight", "medium", "main", "dark", "fontSize", "bold"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DattaAbleLayout.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport { useLocation } from 'react-router-dom';\nimport Da<PERSON><PERSON>bleHeader from './DattaAbleHeader';\nimport DattaAbleSidebar from './DattaAbleSidebar';\nimport DattaAbleFooter from './DattaAbleFooter';\nimport DattaAbleBreadcrumbs from './DattaAbleBreadcrumbs';\n\nimport dattaAbleTheme from '../../theme/dattaAbleTheme';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport '@fontsource/open-sans/300.css';\nimport '@fontsource/open-sans/400.css';\nimport '@fontsource/open-sans/500.css';\nimport '@fontsource/open-sans/600.css';\nimport '@fontsource/open-sans/700.css';\n\ninterface DattaAbleLayoutProps {\n  children: React.ReactNode;\n}\n\nconst DattaAbleLayout: React.FC<DattaAbleLayoutProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const location = useLocation();\n\n  // Close sidebar on route change (mobile)\n  useEffect(() => {\n    setSidebarOpen(false);\n  }, [location.pathname]);\n\n  // Apply CSS variables to document root\n  useEffect(() => {\n    const root = document.documentElement;\n    Object.entries(dattaAbleTheme.cssVariables).forEach(([key, value]) => {\n      root.style.setProperty(key, value);\n    });\n  }, []);\n\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  const toggleSidebarCollapse = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  const layoutStyles = {\n    minHeight: '100vh',\n    backgroundColor: dattaAbleTheme.colors.background.default,\n    fontFamily: dattaAbleTheme.typography.fontFamily,\n  };\n\n  const mainContentStyles: React.CSSProperties = {\n    marginLeft: sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width,\n    transition: 'margin-left 0.3s ease',\n    minHeight: '100vh',\n    display: 'flex',\n    flexDirection: 'column',\n  };\n\n  const contentWrapperStyles: React.CSSProperties = {\n    flex: 1,\n    padding: dattaAbleTheme.spacing[4],\n    paddingTop: `calc(${dattaAbleTheme.layout.header.height} + ${dattaAbleTheme.spacing[4]})`,\n  };\n\n  return (\n    <div style={layoutStyles}>\n      {/* Sidebar */}\n      <DattaAbleSidebar\n        isOpen={sidebarOpen}\n        isCollapsed={sidebarCollapsed}\n        onToggle={toggleSidebar}\n        onCollapse={toggleSidebarCollapse}\n      />\n\n      {/* Main Content Area */}\n      <div \n        style={{\n          ...mainContentStyles,\n          marginLeft: window.innerWidth < 768 ? 0 : (sidebarCollapsed ? dattaAbleTheme.layout.sidebar.collapsedWidth : dattaAbleTheme.layout.sidebar.width)\n        }}\n      >\n        {/* Header */}\n        <DattaAbleHeader\n          onToggleSidebar={toggleSidebar}\n          onToggleSidebarCollapse={toggleSidebarCollapse}\n          sidebarCollapsed={sidebarCollapsed}\n        />\n\n        {/* Content Wrapper */}\n        <div style={contentWrapperStyles}>\n          {/* Breadcrumbs */}\n          <DattaAbleBreadcrumbs />\n\n          {/* Main Content */}\n          <Container fluid className=\"px-0\">\n            <Row>\n              <Col>\n                {children}\n              </Col>\n            </Row>\n          </Container>\n        </div>\n\n        {/* Footer */}\n        <DattaAbleFooter />\n      </div>\n\n\n\n      {/* Mobile Overlay */}\n      {sidebarOpen && window.innerWidth < 768 && (\n        <div\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.5)',\n            zIndex: 1040,\n          }}\n          onClick={toggleSidebar}\n        />\n      )}\n\n      {/* Custom Styles */}\n      <style>{`\n        /* Global body styling for dark theme */\n        body {\n          background-color: ${dattaAbleTheme.colors.background.default} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Override any remaining white backgrounds */\n        * {\n          scrollbar-width: thin;\n          scrollbar-color: ${dattaAbleTheme.colors.text.secondary} ${dattaAbleTheme.colors.background.light};\n        }\n\n        /* Custom scrollbar */\n        ::-webkit-scrollbar {\n          width: 6px;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: ${dattaAbleTheme.colors.background.light};\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: ${dattaAbleTheme.colors.text.secondary};\n          border-radius: ${dattaAbleTheme.borderRadius.full};\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        /* Responsive adjustments */\n        @media (max-width: 767.98px) {\n          .main-content {\n            margin-left: 0 !important;\n          }\n        }\n\n        /* Animation classes */\n        .fade-in {\n          animation: fadeIn 0.3s ease-in;\n        }\n\n        @keyframes fadeIn {\n          from {\n            opacity: 0;\n            transform: translateY(10px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        /* Bootstrap overrides for Datta Able styling */\n        .card {\n          border-radius: ${dattaAbleTheme.borderRadius.lg};\n          box-shadow: ${dattaAbleTheme.shadows.sm};\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .card-header {\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .card-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .text-muted {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Material-UI component overrides */\n        .MuiPaper-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiCard-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiDialog-paper {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiTypography-root {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiTypography-body2 {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Stepper components */\n        .MuiStepper-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label.Mui-active {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiStep-root .MuiStepLabel-label.Mui-completed {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Input and form components */\n        .MuiOutlinedInput-root {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .MuiOutlinedInput-notchedOutline {\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .MuiInputLabel-root {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        /* Alert components */\n        .MuiAlert-root {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .btn {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.medium};\n        }\n\n        .form-control {\n          border-radius: ${dattaAbleTheme.borderRadius.md};\n          border: 1px solid ${dattaAbleTheme.colors.border};\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .form-control:focus {\n          border-color: ${dattaAbleTheme.colors.primary.main};\n          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25;\n          background-color: ${dattaAbleTheme.colors.background.paper};\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        .form-control::placeholder {\n          color: ${dattaAbleTheme.colors.text.secondary};\n        }\n\n        /* Custom utility classes */\n        .text-primary-custom {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .bg-primary-custom {\n          background-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .border-primary-custom {\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        .shadow-custom {\n          box-shadow: ${dattaAbleTheme.shadows.md} !important;\n        }\n\n        /* Wallet specific styling */\n        .wallet-card {\n          background: linear-gradient(135deg, ${dattaAbleTheme.colors.primary.main} 0%, ${dattaAbleTheme.colors.primary.dark} 100%);\n          color: white;\n          border-radius: ${dattaAbleTheme.borderRadius['2xl']};\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n        }\n\n        .wallet-balance {\n          font-size: ${dattaAbleTheme.typography.fontSize['4xl']};\n          font-weight: ${dattaAbleTheme.typography.fontWeight.bold};\n        }\n\n        .stat-card {\n          transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n\n        .stat-card:hover {\n          transform: translateY(-4px);\n          box-shadow: ${dattaAbleTheme.shadows.lg};\n        }\n\n        /* Bootstrap Tab components */\n        .nav-tabs {\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .nav-tabs .nav-link {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n          background-color: transparent !important;\n          border: 1px solid transparent !important;\n        }\n\n        .nav-tabs .nav-link:hover {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        .nav-tabs .nav-link.active {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} ${dattaAbleTheme.colors.border} ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .tab-content {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-radius: 0 0 ${dattaAbleTheme.borderRadius.lg} ${dattaAbleTheme.borderRadius.lg} !important;\n        }\n\n        .tab-pane {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Specific styling for wallet tab content */\n        .tab-pane .card-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Override any remaining white backgrounds in tab content */\n        .tab-content .card-body,\n        .tab-pane .card-body,\n        .nav-tabs + .tab-content {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Modal and Dialog backgrounds */\n        .modal-content {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .modal-header {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .modal-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .modal-footer {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-top: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Dropdown menus */\n        .dropdown-menu {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .dropdown-item {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .dropdown-item:hover {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Table styling */\n        .table {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .table th {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .table td {\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* List group styling */\n        .list-group-item {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Pagination styling */\n        .pagination .page-link {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .pagination .page-link:hover {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .pagination .page-item.active .page-link {\n          background-color: ${dattaAbleTheme.colors.primary.main} !important;\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        /* Badge styling */\n        .badge {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Spinner and loading states */\n        .spinner-border {\n          color: ${dattaAbleTheme.colors.primary.main} !important;\n        }\n\n        /* Toast notifications */\n        .toast {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .toast-header {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Progress bars */\n        .progress {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        /* Breadcrumb styling */\n        .breadcrumb {\n          background-color: transparent !important;\n        }\n\n        .breadcrumb-item a {\n          color: ${dattaAbleTheme.colors.text.secondary} !important;\n        }\n\n        .breadcrumb-item.active {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Input group styling */\n        .input-group-text {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Accordion styling */\n        .accordion-item {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .accordion-header button {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .accordion-body {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Offcanvas styling */\n        .offcanvas {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        .offcanvas-header {\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        /* Popover styling */\n        .popover {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .popover-header {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-bottom: 1px solid ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .popover-body {\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Tooltip styling */\n        .tooltip-inner {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Force all white backgrounds to use theme colors */\n        [style*=\"background-color: white\"],\n        [style*=\"background-color: #ffffff\"],\n        [style*=\"background-color: #fff\"],\n        [style*=\"background: white\"],\n        [style*=\"background: #ffffff\"],\n        [style*=\"background: #fff\"] {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n        }\n\n        /* Ensure all containers and content areas use dark theme */\n        .container,\n        .container-fluid,\n        .row,\n        .col,\n        [class*=\"col-\"] {\n          color: ${dattaAbleTheme.colors.text.primary};\n        }\n\n        /* Override Bootstrap's default white backgrounds */\n        .bg-white {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n        }\n\n        .bg-light {\n          background-color: ${dattaAbleTheme.colors.background.light} !important;\n        }\n\n        /* Ensure form elements use dark theme */\n        .form-select,\n        .form-check-input,\n        .form-range {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          color: ${dattaAbleTheme.colors.text.primary} !important;\n          border-color: ${dattaAbleTheme.colors.border} !important;\n        }\n\n        .form-select:focus,\n        .form-check-input:focus,\n        .form-range:focus {\n          background-color: ${dattaAbleTheme.colors.background.paper} !important;\n          border-color: ${dattaAbleTheme.colors.primary.main} !important;\n          box-shadow: 0 0 0 0.2rem ${dattaAbleTheme.colors.primary.main}25 !important;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default DattaAbleLayout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,KAAQ,iBAAiB,CACrD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,gBAAgB,KAAM,oBAAoB,CACjD,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,oBAAoB,KAAM,wBAAwB,CAEzD,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CACvD,MAAO,sCAAsC,CAC7C,MAAO,+BAA+B,CACtC,MAAO,+BAA+B,CACtC,MAAO,+BAA+B,CACtC,MAAO,+BAA+B,CACtC,MAAO,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMvC,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACnE,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACoB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAAsB,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAE9B;AACAJ,SAAS,CAAC,IAAM,CACdkB,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAAE,CAACG,QAAQ,CAACC,QAAQ,CAAC,CAAC,CAEvB;AACAtB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuB,IAAI,CAAGC,QAAQ,CAACC,eAAe,CACrCC,MAAM,CAACC,OAAO,CAAClB,cAAc,CAACmB,YAAY,CAAC,CAACC,OAAO,CAACC,KAAA,EAAkB,IAAjB,CAACC,GAAG,CAAEC,KAAK,CAAC,CAAAF,KAAA,CAC/DP,IAAI,CAACU,KAAK,CAACC,WAAW,CAACH,GAAG,CAAEC,KAAK,CAAC,CACpC,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAG,aAAa,CAAGA,CAAA,GAAM,CAC1BjB,cAAc,CAAC,CAACD,WAAW,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAmB,qBAAqB,CAAGA,CAAA,GAAM,CAClChB,mBAAmB,CAAC,CAACD,gBAAgB,CAAC,CACxC,CAAC,CAED,KAAM,CAAAkB,YAAY,CAAG,CACnBC,SAAS,CAAE,OAAO,CAClBC,eAAe,CAAE9B,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACC,OAAO,CACzDC,UAAU,CAAElC,cAAc,CAACmC,UAAU,CAACD,UACxC,CAAC,CAED,KAAM,CAAAE,iBAAsC,CAAG,CAC7CC,UAAU,CAAE3B,gBAAgB,CAAGV,cAAc,CAACsC,MAAM,CAACC,OAAO,CAACC,cAAc,CAAGxC,cAAc,CAACsC,MAAM,CAACC,OAAO,CAACE,KAAK,CACjHC,UAAU,CAAE,uBAAuB,CACnCb,SAAS,CAAE,OAAO,CAClBc,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CAAC,CAED,KAAM,CAAAC,oBAAyC,CAAG,CAChDC,IAAI,CAAE,CAAC,CACPC,OAAO,CAAE/C,cAAc,CAACgD,OAAO,CAAC,CAAC,CAAC,CAClCC,UAAU,CAAE,QAAQjD,cAAc,CAACsC,MAAM,CAACY,MAAM,CAACC,MAAM,MAAMnD,cAAc,CAACgD,OAAO,CAAC,CAAC,CAAC,GACxF,CAAC,CAED,mBACE5C,KAAA,QAAKoB,KAAK,CAAEI,YAAa,CAAArB,QAAA,eAEvBL,IAAA,CAACL,gBAAgB,EACfuD,MAAM,CAAE5C,WAAY,CACpB6C,WAAW,CAAE3C,gBAAiB,CAC9B4C,QAAQ,CAAE5B,aAAc,CACxB6B,UAAU,CAAE5B,qBAAsB,CACnC,CAAC,cAGFvB,KAAA,QACEoB,KAAK,CAAE,CACL,GAAGY,iBAAiB,CACpBC,UAAU,CAAEmB,MAAM,CAACC,UAAU,CAAG,GAAG,CAAG,CAAC,CAAI/C,gBAAgB,CAAGV,cAAc,CAACsC,MAAM,CAACC,OAAO,CAACC,cAAc,CAAGxC,cAAc,CAACsC,MAAM,CAACC,OAAO,CAACE,KAC7I,CAAE,CAAAlC,QAAA,eAGFL,IAAA,CAACN,eAAe,EACd8D,eAAe,CAAEhC,aAAc,CAC/BiC,uBAAuB,CAAEhC,qBAAsB,CAC/CjB,gBAAgB,CAAEA,gBAAiB,CACpC,CAAC,cAGFN,KAAA,QAAKoB,KAAK,CAAEqB,oBAAqB,CAAAtC,QAAA,eAE/BL,IAAA,CAACH,oBAAoB,GAAE,CAAC,cAGxBG,IAAA,CAACV,SAAS,EAACoE,KAAK,MAACC,SAAS,CAAC,MAAM,CAAAtD,QAAA,cAC/BL,IAAA,CAACT,GAAG,EAAAc,QAAA,cACFL,IAAA,CAACR,GAAG,EAAAa,QAAA,CACDA,QAAQ,CACN,CAAC,CACH,CAAC,CACG,CAAC,EACT,CAAC,cAGNL,IAAA,CAACJ,eAAe,GAAE,CAAC,EAChB,CAAC,CAKLU,WAAW,EAAIgD,MAAM,CAACC,UAAU,CAAG,GAAG,eACrCvD,IAAA,QACEsB,KAAK,CAAE,CACLsC,QAAQ,CAAE,OAAO,CACjBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTpC,eAAe,CAAE,oBAAoB,CACrCqC,MAAM,CAAE,IACV,CAAE,CACFC,OAAO,CAAE1C,aAAc,CACxB,CACF,cAGDxB,IAAA,UAAAK,QAAA,CAAQ;AACd;AACA;AACA,8BAA8BP,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACC,OAAO;AACtE,mBAAmBjC,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA,6BAA6BtE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACE,SAAS,IAAIvE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBxE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AAC9D;AACA;AACA;AACA,wBAAwBxE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACE,SAAS;AAC5D,2BAA2BvE,cAAc,CAACyE,YAAY,CAACC,IAAI;AAC3D;AACA;AACA;AACA,wBAAwB1E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2BtE,cAAc,CAACyE,YAAY,CAACE,EAAE;AACzD,wBAAwB3E,cAAc,CAAC4E,OAAO,CAACC,EAAE;AACjD,8BAA8B7E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AAC1D,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,qCAAqC/E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACjE,mBAAmB9E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,mBAAmBtE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,mBAAmBtE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,mBAAmBtE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE;AACA;AACA;AACA,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,mBAAmBtE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,0BAA0BtE,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACtD;AACA;AACA;AACA,mBAAmB9E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA;AACA,8BAA8BvE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE,mBAAmBxE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,2BAA2BtE,cAAc,CAACyE,YAAY,CAACO,EAAE;AACzD,yBAAyBhF,cAAc,CAACmC,UAAU,CAAC8C,UAAU,CAACC,MAAM;AACpE;AACA;AACA;AACA,2BAA2BlF,cAAc,CAACyE,YAAY,CAACO,EAAE;AACzD,8BAA8BhF,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AAC1D,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,0BAA0BtE,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AAC5D,qCAAqCnF,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AACvE,8BAA8BnF,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,mBAAmBtE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AACrD;AACA;AACA;AACA,8BAA8BnF,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AAChE;AACA;AACA;AACA,0BAA0BnF,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AAC5D;AACA;AACA;AACA,wBAAwBnF,cAAc,CAAC4E,OAAO,CAACI,EAAE;AACjD;AACA;AACA;AACA;AACA,gDAAgDhF,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI,QAAQnF,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACc,IAAI;AAC5H;AACA,2BAA2BpF,cAAc,CAACyE,YAAY,CAAC,KAAK,CAAC;AAC7D,wBAAwBzE,cAAc,CAAC4E,OAAO,CAACD,EAAE;AACjD;AACA;AACA;AACA,uBAAuB3E,cAAc,CAACmC,UAAU,CAACkD,QAAQ,CAAC,KAAK,CAAC;AAChE,yBAAyBrF,cAAc,CAACmC,UAAU,CAAC8C,UAAU,CAACK,IAAI;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBtF,cAAc,CAAC4E,OAAO,CAACD,EAAE;AACjD;AACA;AACA;AACA;AACA,qCAAqC3E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACjE;AACA;AACA;AACA,mBAAmB9E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE;AACA;AACA;AACA,mBAAmBxE,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AACrD,8BAA8BnF,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,0BAA0B/E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM,IAAI9E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM,IAAI9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AAChI;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,+BAA+B/E,cAAc,CAACyE,YAAY,CAACE,EAAE,IAAI3E,cAAc,CAACyE,YAAY,CAACE,EAAE;AAC/F;AACA;AACA;AACA,8BAA8B3E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,qCAAqC/E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACjE;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,kCAAkC/E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AAC9D;AACA;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,8BAA8B/E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AAC1D;AACA;AACA;AACA,mBAAmB9E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE,mBAAmBxE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,mBAAmBtE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE,mBAAmBxE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD,0BAA0BtE,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACtD;AACA;AACA;AACA,0BAA0B9E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACtD;AACA;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD,0BAA0BtE,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACtD;AACA;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD,0BAA0BtE,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACtD;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE,mBAAmBxE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AAChE,0BAA0BnF,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AAC5D;AACA;AACA;AACA;AACA,8BAA8BnF,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE,mBAAmBxE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,mBAAmBtE,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AACrD;AACA;AACA;AACA;AACA,8BAA8BnF,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD,8BAA8BtE,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AAC1D;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE,mBAAmBxE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD,qCAAqCtE,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACjE;AACA;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBxE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACE,SAAS;AACvD;AACA;AACA;AACA,mBAAmBvE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE,mBAAmBxE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD,0BAA0BtE,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACtD;AACA;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,0BAA0B/E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACtD;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA,qCAAqCtE,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACjE;AACA;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,0BAA0B/E,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACtD;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE,mBAAmBxE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD,qCAAqCtE,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACjE;AACA;AACA;AACA,mBAAmB9E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBtE,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD;AACA;AACA;AACA;AACA,8BAA8BtE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE;AACA;AACA;AACA,8BAA8B/E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAACwC,KAAK;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8BxE,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,mBAAmB/E,cAAc,CAAC+B,MAAM,CAACsC,IAAI,CAACC,OAAO;AACrD,0BAA0BtE,cAAc,CAAC+B,MAAM,CAAC+C,MAAM;AACtD;AACA;AACA;AACA;AACA;AACA,8BAA8B9E,cAAc,CAAC+B,MAAM,CAACC,UAAU,CAAC+C,KAAK;AACpE,0BAA0B/E,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AAC5D,qCAAqCnF,cAAc,CAAC+B,MAAM,CAACuC,OAAO,CAACa,IAAI;AACvE;AACA,OAAO,CAAQ,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9E,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}