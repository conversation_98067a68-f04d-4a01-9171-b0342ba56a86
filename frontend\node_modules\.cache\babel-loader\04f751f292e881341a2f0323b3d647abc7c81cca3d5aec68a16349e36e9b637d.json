{"ast": null, "code": "'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The StaticDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { StaticDateTimePicker } from '@mui/x-date-pickers'`\", \"or `import { StaticDateTimePicker } from '@mui/x-date-pickers/StaticDateTimePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The StaticDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst StaticDateTimePicker = /*#__PURE__*/React.forwardRef(function DeprecatedStaticDateTimePicker() {\n  warn();\n  return null;\n});\nexport default StaticDateTimePicker;", "map": {"version": 3, "names": ["React", "warnedOnce", "warn", "console", "join", "StaticDateTimePicker", "forwardRef", "DeprecatedStaticDateTimePicker"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/StaticDateTimePicker/StaticDateTimePicker.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The StaticDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { StaticDateTimePicker } from '@mui/x-date-pickers'`\", \"or `import { StaticDateTimePicker } from '@mui/x-date-pickers/StaticDateTimePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n/**\n * @deprecated The StaticDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`. More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.\n * @ignore - do not document.\n */\nconst StaticDateTimePicker = /*#__PURE__*/React.forwardRef(function DeprecatedStaticDateTimePicker() {\n  warn();\n  return null;\n});\nexport default StaticDateTimePicker;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,UAAU,GAAG,KAAK;AACtB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,IAAI,CAACD,UAAU,EAAE;IACfE,OAAO,CAACD,IAAI,CAAC,CAAC,6FAA6F,EAAE,EAAE,EAAE,6EAA6E,EAAE,sFAAsF,EAAE,EAAE,EAAE,qGAAqG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9YH,UAAU,GAAG,IAAI;EACnB;AACF,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMI,oBAAoB,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASC,8BAA8BA,CAAA,EAAG;EACnGL,IAAI,CAAC,CAAC;EACN,OAAO,IAAI;AACb,CAAC,CAAC;AACF,eAAeG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}