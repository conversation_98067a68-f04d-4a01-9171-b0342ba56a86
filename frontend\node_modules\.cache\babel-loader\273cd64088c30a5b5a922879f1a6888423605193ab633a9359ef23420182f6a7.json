{"ast": null, "code": "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineDotUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineDot', slot);\n}\nconst timelineDotClasses = generateUtilityClasses('MuiTimelineDot', ['root', 'filled', 'outlined', 'filledGrey', 'outlinedGrey', 'filledPrimary', 'outlinedPrimary', 'filledSecondary', 'outlinedSecondary']);\nexport default timelineDotClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTimelineDotUtilityClass", "slot", "timelineDotClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/lab/esm/TimelineDot/timelineDotClasses.js"], "sourcesContent": ["import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getTimelineDotUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineDot', slot);\n}\nconst timelineDotClasses = generateUtilityClasses('MuiTimelineDot', ['root', 'filled', 'outlined', 'filledGrey', 'outlinedGrey', 'filledPrimary', 'outlinedPrimary', 'filledSecondary', 'outlinedSecondary']);\nexport default timelineDotClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;AAC7M,eAAeG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}